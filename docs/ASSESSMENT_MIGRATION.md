# Assessment Criteria Migration Guide

This guide covers the database migration for the new configurable assessment criteria system.

## Overview

The assessment criteria migration adds:
- **New Table**: `assessment_templates` for storing predefined assessment templates
- **New Fields**: `assessmentCriteria` and `assessmentTemplateId` in `event_details` table
- **New Enums**: `AssessmentRoleType` and `AssessmentScaleType`
- **Default Templates**: 5 predefined assessment templates for common use cases

## Migration Files

### 1. Main Migration
```
prisma/migrations/20241223000000_add_assessment_criteria/migration.sql
```

### 2. Migration Script
```
scripts/migrate-assessment-criteria.ts
```

## Running the Migration

### Option 1: Using npm script (Recommended)
```bash
npm run migrate:assessment
```

### Option 2: Using Prisma directly
```bash
npx prisma migrate deploy
```

### Option 3: Manual execution
```bash
npx ts-node scripts/migrate-assessment-criteria.ts
```

## What Gets Created

### 1. Database Schema Changes

#### New Table: `assessment_templates`
```sql
CREATE TABLE "assessment_templates" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "roleType" "AssessmentRoleType" NOT NULL,
    "criteria" JSONB NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "organizationId" TEXT,
    "createdById" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "assessment_templates_pkey" PRIMARY KEY ("id")
);
```

#### New Fields in `event_details`
```sql
ALTER TABLE "event_details" 
ADD COLUMN "assessmentCriteria" JSONB,
ADD COLUMN "assessmentTemplateId" TEXT;
```

#### New Enums
```sql
CREATE TYPE "AssessmentRoleType" AS ENUM (
    'TECHNICAL', 'NON_TECHNICAL', 'SALES', 'LANGUAGE', 
    'LEADERSHIP', 'CUSTOMER_SERVICE', 'MARKETING', 'FINANCE', 'CUSTOM'
);

CREATE TYPE "AssessmentScaleType" AS ENUM ('NUMERIC', 'CATEGORICAL', 'BOOLEAN');
```

### 2. Default Templates Created

The migration creates 5 default assessment templates:

#### 1. Technical Roles Assessment
- **Role Type**: TECHNICAL
- **Criteria**: Problem Solving, Technical Knowledge, Code Quality, System Design, Communication
- **Use Case**: Software engineers, developers, technical roles

#### 2. Language Proficiency Assessment  
- **Role Type**: LANGUAGE
- **Criteria**: Speaking Fluency (CEFR), Grammar, Vocabulary, Pronunciation, Comprehension
- **Use Case**: Language teachers, international roles, customer service

#### 3. Sales Roles Assessment
- **Role Type**: SALES  
- **Criteria**: Negotiation, Relationship Building, Product Knowledge, Persuasion, Objection Handling
- **Use Case**: Sales representatives, account managers, business development

#### 4. Leadership Assessment
- **Role Type**: LEADERSHIP
- **Criteria**: Team Management, Strategic Thinking, Decision Making, Conflict Resolution, Vision Communication
- **Use Case**: Managers, directors, leadership positions

#### 5. Non-Technical Roles Assessment
- **Role Type**: NON_TECHNICAL
- **Criteria**: Communication, Analytical Thinking, Teamwork, Adaptability, Initiative
- **Use Case**: Business roles, administrative positions, general roles

## Verification

After running the migration, verify it was successful:

```bash
# Check template count
npx prisma studio
# Navigate to assessment_templates table

# Or use the verification script
npm run migrate:assessment
```

Expected output:
```
✅ Found 5 default templates:
   - Technical Roles Assessment (TECHNICAL)
   - Language Proficiency Assessment (LANGUAGE)  
   - Sales Roles Assessment (SALES)
   - Leadership Assessment (LEADERSHIP)
   - Non-Technical Roles Assessment (NON_TECHNICAL)
```

## Rollback (if needed)

To rollback the data (removes templates but keeps schema):
```bash
npm run migrate:assessment:rollback
```

⚠️ **Warning**: This only removes the template data. To fully rollback the schema changes, you would need to create a new migration.

## Usage After Migration

### 1. Interview Creation
Users can now select assessment templates or create custom criteria:

```typescript
// Template-based assessment
{
  assessmentTemplateId: "template_technical_001",
  assessmentCriteria: undefined
}

// Custom assessment
{
  assessmentTemplateId: undefined,
  assessmentCriteria: [
    {
      id: "custom_criterion_1",
      name: "Custom Skill",
      description: "Evaluate custom skill",
      scale: { type: "numeric", min: 1, max: 10 },
      weight: 50,
      category: "technical",
      isRequired: true
    }
  ]
}
```

### 2. AI Feedback Generation
The AI will automatically use the configured criteria:

```typescript
// Retrieves criteria from EventDetails
const assessmentCriteria = interview.eventDetails?.assessmentCriteria || 
                          interview.eventDetails?.assessmentTemplate?.criteria;

// Generates dynamic prompt with criteria
const prompt = generateDynamicAssessmentPrompt({
  assessmentCriteria,
  role,
  level,
  // ... other params
});
```

### 3. Feedback Display
The UI automatically adapts to show custom criteria:

```typescript
<DynamicAssessmentFeedbackDisplay
  feedback={feedback}
  assessmentCriteria={criteria}
  isLegacyFormat={!feedback?.assessment_results}
/>
```

## Troubleshooting

### Common Issues

1. **Migration fails with permission error**
   ```
   Solution: Ensure your database user has CREATE TABLE permissions
   ```

2. **TypeScript errors after migration**
   ```bash
   npx prisma generate
   ```

3. **Templates not appearing in UI**
   ```
   Check: Verify templates were created with isDefault=true and isActive=true
   ```

4. **Assessment criteria not saving**
   ```
   Check: Ensure EventDetails.assessmentCriteria field exists and accepts JSONB
   ```

### Validation Queries

```sql
-- Check if migration completed
SELECT COUNT(*) FROM assessment_templates WHERE "isDefault" = true;
-- Should return 5

-- Check EventDetails schema
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'event_details' 
AND column_name IN ('assessmentCriteria', 'assessmentTemplateId');

-- Check enum types
SELECT enumlabel FROM pg_enum WHERE enumtypid = 'AssessmentRoleType'::regtype;
```

## Support

If you encounter issues:
1. Check the migration logs for specific error messages
2. Verify your database connection and permissions
3. Ensure you're running the latest Prisma version
4. Check the troubleshooting section above

For additional help, contact the development team with:
- Error messages
- Database type and version
- Migration logs
- Current schema state
