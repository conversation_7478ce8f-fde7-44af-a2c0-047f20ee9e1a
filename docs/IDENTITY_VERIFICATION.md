# Identity Verification System

## Overview

The interview onboarding process now includes a comprehensive identity verification system that ensures candidate authenticity through multiple verification steps.

## Flow Overview

The new onboarding flow consists of 4 main steps:

1. **Personal Information Collection** - Candidate provides their full name
2. **ID Document Capture** - Candidate photographs their government-issued ID
3. **Face Verification** - Candidate takes a selfie for facial recognition comparison
4. **Interview Welcome** - Verified candidates proceed to the interview

## Components

### 1. CandidateNameForm (`components/onboarding-interview/candidate-name-form.tsx`)
- Collects candidate's full name
- Validates name format and length
- Creates/updates user profile with the provided name
- Shows progress indicator

### 2. IdDocumentCapture (`components/onboarding-interview/id-document-capture.tsx`)
- Captures photo of government-issued ID document
- Provides visual guides for proper document positioning
- Supports retaking photos if needed
- Validates image quality

### 3. FaceVerification (`components/onboarding-interview/face-verification.tsx`)
- Captures candidate's face photo
- Performs facial recognition comparison with ID document
- Shows verification results with confidence scores
- Prevents proceeding without successful verification

### 4. Enhanced Welcome Screen (`components/onboarding-interview/combined-welcome-screen.tsx`)
- Displays verification completion status
- Shows personalized welcome with candidate's name
- Indicates all verification steps are complete

## API Endpoints

### `/api/candidate/verify-identity`
- **Method**: POST
- **Purpose**: Performs face verification between selfie and ID document
- **Input**: 
  ```json
  {
    "facePhoto": "data:image/jpeg;base64,...",
    "idDocument": "data:image/jpeg;base64,..."
  }
  ```
- **Output**:
  ```json
  {
    "verified": true,
    "confidence": 0.87,
    "message": "Identity verified successfully. Face match confidence: 87%",
    "details": {
      "faceDetected": true,
      "idFaceDetected": true,
      "similarity": 0.87,
      "threshold": 0.75
    }
  }
  ```

## Utilities

### Face Verification Utils (`utils/face-verification.ts`)
- `performFaceVerification()` - Main verification function
- `simulateFaceDetection()` - Face detection simulation
- `simulateFaceComparison()` - Face comparison simulation
- `isValidBase64Image()` - Image format validation
- `resizeImage()` - Image optimization
- `checkImageQuality()` - Image quality assessment

## Security Features

### Current Implementation (Development/Demo)
- Simulated face detection with 95% success rate
- Simulated face comparison with realistic confidence scores
- Image format validation
- Basic quality checks

### Production Recommendations
For production deployment, integrate with professional face verification services:

#### AWS Rekognition
```javascript
const rekognition = new AWS.Rekognition({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

const result = await rekognition.compareFaces({
  SourceImage: { Bytes: faceBuffer },
  TargetImage: { Bytes: idBuffer },
  SimilarityThreshold: 75,
}).promise();
```

#### Azure Face API
```javascript
const faceClient = new FaceClient(
  new CognitiveServicesCredentials(process.env.AZURE_FACE_API_KEY),
  process.env.AZURE_FACE_ENDPOINT
);

const verifyResult = await faceClient.face.verifyFaceToFace(
  faceId1,
  faceId2
);
```

#### Google Cloud Vision API
```javascript
const vision = require('@google-cloud/vision');
const client = new vision.ImageAnnotatorClient();

const [result] = await client.faceDetection({
  image: { content: imageBuffer },
});
```

## Configuration

### Environment Variables
```env
# For AWS Rekognition
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1

# For Azure Face API
AZURE_FACE_API_KEY=your_api_key
AZURE_FACE_ENDPOINT=https://your-region.api.cognitive.microsoft.com/

# For Google Cloud Vision
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
```

### Verification Thresholds
- **Face Detection Confidence**: 85% minimum
- **Face Comparison Threshold**: 75% similarity
- **Image Quality Score**: 70% minimum

## User Experience

### Progress Indicators
Each step shows clear progress with:
- Step numbers (1-4)
- Completion checkmarks
- Current step highlighting
- Step descriptions

### Error Handling
- Camera access denied
- Poor image quality
- Face detection failures
- Network connectivity issues
- Verification threshold not met

### Accessibility
- Clear instructions at each step
- Visual guides for photo capture
- Error messages with actionable guidance
- Keyboard navigation support

## Testing

### Manual Testing
1. Navigate to `/onboarding-interview?id=test`
2. Complete each verification step
3. Verify progress indicators update correctly
4. Test error scenarios (poor lighting, no face, etc.)

### Automated Testing
```javascript
// Test face verification API
const response = await fetch('/api/candidate/verify-identity', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    facePhoto: validFacePhotoBase64,
    idDocument: validIdDocumentBase64,
  }),
});

expect(response.status).toBe(200);
const result = await response.json();
expect(result.verified).toBe(true);
```

## Future Enhancements

### Planned Features
1. **Liveness Detection** - Ensure photos are taken live, not from existing images
2. **Document OCR** - Extract and verify information from ID documents
3. **Biometric Templates** - Store encrypted biometric templates for future verification
4. **Multi-factor Authentication** - Combine with SMS/email verification
5. **Audit Logging** - Comprehensive logging of all verification attempts

### Integration Opportunities
- **HRMS Integration** - Sync verified candidate data with HR systems
- **Background Check Services** - Trigger background checks post-verification
- **Video Interview Platforms** - Pre-verify candidates before video interviews
- **Compliance Reporting** - Generate verification reports for compliance

## Troubleshooting

### Common Issues
1. **Camera not working**: Check browser permissions and HTTPS requirement
2. **Verification failing**: Ensure good lighting and clear face visibility
3. **API errors**: Check network connectivity and authentication
4. **Image quality issues**: Guide users on proper photo capture techniques

### Debug Mode
Enable debug logging by setting:
```env
DEBUG_FACE_VERIFICATION=true
```

This will log detailed information about:
- Image processing steps
- Face detection results
- Comparison scores
- Error details
