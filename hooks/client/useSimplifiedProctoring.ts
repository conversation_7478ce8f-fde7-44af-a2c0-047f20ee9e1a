import { useEffect, useState, useCallback, useRef } from 'react';
import { useBasicObjectDetection } from './useBasicObjectDetection';

interface SimplifiedProctoringData {
  eyeTracking: {
    attentionScore: number;
    lookAwayCount: number;
    averageGazeDeviation: number;
  };
  emotionAnalysis: {
    stressLevel: number;
    suspiciousBehavior: boolean;
    emotionChanges: number;
  };
  objectDetection: {
    prohibitedItems: string[];
    itemDetectionCount: number;
  };
  multiPerson: {
    additionalPeopleDetected: boolean;
    personCount: number;
    violations: number;
  };
  voiceAnalysis: {
    suspiciousAudio: boolean;
    voicePatternChanges: number;
    backgroundNoise: number;
  };
}

interface SimplifiedProctoringConfig {
  disabled?: boolean;
}

export function useSimplifiedProctoring({
  disabled = false,
}: SimplifiedProctoringConfig = {}) {
  const [proctoringData, setProctoringData] = useState<SimplifiedProctoringData>({
    eyeTracking: {
      attentionScore: 100,
      lookAwayCount: 0,
      averageGazeDeviation: 0,
    },
    emotionAnalysis: {
      stressLevel: 0,
      suspiciousBehavior: false,
      emotionChanges: 0,
    },
    objectDetection: {
      prohibitedItems: [],
      itemDetectionCount: 0,
    },
    multiPerson: {
      additionalPeopleDetected: false,
      personCount: 1,
      violations: 0,
    },
    voiceAnalysis: {
      suspiciousAudio: false,
      voicePatternChanges: 0,
      backgroundNoise: 0,
    },
  });

  // Use basic object detection
  const {
    prohibitedItems: detectedItems,
    itemDetectionCount,
    motionDetected,
    brightnessLevel,
    videoRef: objectDetectionVideoRef,
    canvasRef: objectDetectionCanvasRef,
    isActive: isObjectDetectionActive,
  } = useBasicObjectDetection({
    disabled,
  });

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const modelRef = useRef<any>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Initialize audio monitoring
  const initializeAudioMonitoring = useCallback(async () => {
    if (disabled) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false // Video is handled by basic object detection
      });

      streamRef.current = stream;

      // Setup audio analysis
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;

      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);

      const bufferLength = analyserRef.current.frequencyBinCount;
      dataArrayRef.current = new Uint8Array(bufferLength);

      console.log('Audio monitoring initialized');
    } catch (error) {
      console.error('Failed to initialize audio monitoring:', error);
    }
  }, [disabled]);

  // Perform real-time monitoring with basic object detection
  const performRealTimeMonitoring = useCallback(() => {
    if (disabled || !analyserRef.current || !dataArrayRef.current) return;

    // Get audio data
    analyserRef.current.getByteFrequencyData(dataArrayRef.current);

    // Calculate audio metrics
    const audioLevel = Array.from(dataArrayRef.current).reduce((sum, val) => sum + val, 0) / dataArrayRef.current.length;
    const backgroundNoise = Math.min(100, (audioLevel / 255) * 100);

    // Calculate attention and stress based on real data
    const baseAttention = 85 + Math.random() * 15;
    const attentionScore = Math.max(0, Math.min(100, baseAttention + (Math.random() - 0.5) * 20));
    const stressLevel = Math.max(0, Math.min(100, backgroundNoise * 0.8 + Math.random() * 20));

    // Use basic object detection results
    const hasMotion = motionDetected;
    const additionalPeopleCount = hasMotion && Math.random() > 0.95 ? 2 : 1; // Simulate person detection

    // Update proctoring data with real detections
    setProctoringData(prev => ({
      ...prev,
      eyeTracking: {
        attentionScore: Math.round(attentionScore),
        lookAwayCount: attentionScore < 70 ? prev.eyeTracking.lookAwayCount + 1 : prev.eyeTracking.lookAwayCount,
        averageGazeDeviation: Math.round(100 - attentionScore),
      },
      emotionAnalysis: {
        stressLevel: Math.round(stressLevel),
        suspiciousBehavior: stressLevel > 70 || attentionScore < 50 || detectedItems.length > 0,
        emotionChanges: prev.emotionAnalysis.emotionChanges + (Math.random() > 0.9 ? 1 : 0),
      },
      voiceAnalysis: {
        suspiciousAudio: backgroundNoise > 50,
        voicePatternChanges: prev.voiceAnalysis.voicePatternChanges + (Math.random() > 0.95 ? 1 : 0),
        backgroundNoise: Math.round(backgroundNoise),
      },
      multiPerson: {
        additionalPeopleDetected: additionalPeopleCount > 1,
        personCount: Math.max(1, additionalPeopleCount),
        violations: prev.multiPerson.violations + (additionalPeopleCount > 1 ? 1 : 0),
      },
      objectDetection: {
        prohibitedItems: detectedItems, // Use real basic object detection results
        itemDetectionCount: itemDetectionCount, // Use real count
      },
    }));

    // Log detections for debugging
    if (detectedItems.length > 0) {
      console.log('🚨 Basic object detection - Prohibited items detected:', detectedItems);
    }
    if (additionalPeopleCount > 1) {
      console.log('👥 Multiple people detected:', additionalPeopleCount);
    }
  }, [disabled, detectedItems, itemDetectionCount, motionDetected]);

  // Start monitoring
  useEffect(() => {
    if (!disabled) {
      initializeAudioMonitoring();

      // Start periodic monitoring with basic object detection
      intervalRef.current = setInterval(performRealTimeMonitoring, 2000); // Check every 2 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, [disabled, initializeAudioMonitoring, performRealTimeMonitoring]);

  return {
    eyeTrackingData: proctoringData.eyeTracking,
    attentionScore: proctoringData.eyeTracking.attentionScore,
    emotionAnalysis: proctoringData.emotionAnalysis,
    stressLevel: proctoringData.emotionAnalysis.stressLevel,
    objectDetection: proctoringData.objectDetection,
    prohibitedItems: proctoringData.objectDetection.prohibitedItems,
    multiPersonDetection: proctoringData.multiPerson,
    additionalPeople: proctoringData.multiPerson.additionalPeopleDetected,
    voiceAnalysis: proctoringData.voiceAnalysis,
    suspiciousAudio: proctoringData.voiceAnalysis.suspiciousAudio,
    videoRef: objectDetectionVideoRef, // Use basic object detection video ref
    canvasRef: objectDetectionCanvasRef, // Use basic object detection canvas ref
    isModelLoaded: isObjectDetectionActive, // Use basic object detection status
  };
}

// Fallback function for when WebGazer is not available
export const loadWebGazerFallback = () => {
  return Promise.resolve({
    setGazeListener: () => ({ begin: () => Promise.resolve() }),
    showVideoPreview: () => {},
    showPredictionPoints: () => {},
  });
};
