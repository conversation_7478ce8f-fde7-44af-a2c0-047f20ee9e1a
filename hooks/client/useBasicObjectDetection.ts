import { useEffect, useState, useCallback, useRef } from 'react';

interface BasicObjectDetectionData {
  prohibitedItems: string[];
  itemDetectionCount: number;
  motionDetected: boolean;
  brightnessLevel: number;
  colorAnalysis: {
    dominantColors: string[];
    hasPhoneColors: boolean;
    hasScreenGlow: boolean;
  };
}

interface BasicObjectDetectionConfig {
  disabled?: boolean;
  sensitivity?: number;
}

export function useBasicObjectDetection({
  disabled = false,
  sensitivity = 0.7,
}: BasicObjectDetectionConfig = {}) {
  const [detectionData, setDetectionData] = useState<BasicObjectDetectionData>({
    prohibitedItems: [],
    itemDetectionCount: 0,
    motionDetected: false,
    brightnessLevel: 0,
    colorAnalysis: {
      dominantColors: [],
      hasPhoneColors: false,
      hasScreenGlow: false,
    },
  });

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const previousFrameRef = useRef<ImageData | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Initialize camera
  const initializeCamera = useCallback(async () => {
    if (disabled) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480 },
        audio: false,
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      console.log('Basic object detection camera initialized');
    } catch (error) {
      console.error('Failed to initialize camera:', error);
    }
  }, [disabled]);

  // Analyze frame for basic object detection
  const analyzeFrame = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || disabled) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const currentFrame = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = currentFrame.data;

    // Analyze brightness
    let totalBrightness = 0;
    let redTotal = 0;
    let greenTotal = 0;
    let blueTotal = 0;
    const pixelCount = pixels.length / 4;

    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      
      redTotal += r;
      greenTotal += g;
      blueTotal += b;
      
      // Calculate brightness using luminance formula
      const brightness = (0.299 * r + 0.587 * g + 0.114 * b);
      totalBrightness += brightness;
    }

    const avgBrightness = totalBrightness / pixelCount;
    const avgRed = redTotal / pixelCount;
    const avgGreen = greenTotal / pixelCount;
    const avgBlue = blueTotal / pixelCount;

    // Detect motion by comparing with previous frame
    let motionDetected = false;
    if (previousFrameRef.current) {
      const prevPixels = previousFrameRef.current.data;
      let diffSum = 0;
      
      for (let i = 0; i < pixels.length; i += 4) {
        const rDiff = Math.abs(pixels[i] - prevPixels[i]);
        const gDiff = Math.abs(pixels[i + 1] - prevPixels[i + 1]);
        const bDiff = Math.abs(pixels[i + 2] - prevPixels[i + 2]);
        diffSum += (rDiff + gDiff + bDiff) / 3;
      }
      
      const avgDiff = diffSum / pixelCount;
      motionDetected = avgDiff > 10; // Threshold for motion detection
    }

    // Store current frame for next comparison
    previousFrameRef.current = currentFrame;

    // Advanced object detection with better discrimination
    const dominantColors: string[] = [];
    let hasPhoneColors = false;
    let hasScreenGlow = false;
    let hasLaptopColors = false;

    // Calculate color ratios for better object discrimination
    const colorVariance = Math.sqrt(
      Math.pow(avgRed - (avgRed + avgGreen + avgBlue) / 3, 2) +
      Math.pow(avgGreen - (avgRed + avgGreen + avgBlue) / 3, 2) +
      Math.pow(avgBlue - (avgRed + avgGreen + avgBlue) / 3, 2)
    );

    // Phone detection: Small dark object with low color variance (solid colors)
    const isPhoneLike = avgBrightness < 80 &&
                       colorVariance < 15 &&
                       (avgRed < 70 && avgGreen < 70 && avgBlue < 70) &&
                       motionDetected;

    // Laptop detection: Larger object with mixed colors and higher brightness
    const isLaptopLike = avgBrightness > 100 && avgBrightness < 180 &&
                        colorVariance > 20 &&
                        Math.abs(avgRed - avgGreen) < 30 &&
                        motionDetected;

    // Screen glow: Very bright with blue/white dominance
    const isScreenGlow = avgBrightness > 200 &&
                        (avgBlue > avgRed + 20 || avgBlue > avgGreen + 20) &&
                        colorVariance > 25;

    // Book detection: Medium brightness with low color variance
    const isBookLike = avgBrightness > 120 && avgBrightness < 200 &&
                      colorVariance < 20 &&
                      !motionDetected; // Books are usually stationary

    // Update color flags
    if (isPhoneLike) {
      dominantColors.push('phone-dark');
      hasPhoneColors = true;
    }

    if (isLaptopLike) {
      dominantColors.push('laptop-mixed');
      hasLaptopColors = true;
    }

    if (isScreenGlow) {
      dominantColors.push('screen-glow');
      hasScreenGlow = true;
    }

    if (isBookLike) {
      dominantColors.push('book-paper');
    }

    // Detect prohibited items with improved logic
    const prohibitedItems: string[] = [];
    let newDetectionCount = 0;

    // Only detect phone if it's specifically phone-like (not laptop-like)
    if (isPhoneLike && !isLaptopLike && !isScreenGlow) {
      prohibitedItems.push('phone');
      newDetectionCount++;
    }

    // Detect laptop only if it's laptop-like
    if (isLaptopLike && !isPhoneLike) {
      prohibitedItems.push('laptop');
      newDetectionCount++;
    }

    // Detect screen glow
    if (isScreenGlow) {
      prohibitedItems.push('screen');
      newDetectionCount++;
    }

    // Detect books
    if (isBookLike && avgBrightness > 150) {
      prohibitedItems.push('book');
      newDetectionCount++;
    }

    // Update detection data
    setDetectionData(prev => ({
      prohibitedItems,
      itemDetectionCount: prev.itemDetectionCount + newDetectionCount,
      motionDetected,
      brightnessLevel: Math.round(avgBrightness),
      colorAnalysis: {
        dominantColors,
        hasPhoneColors,
        hasScreenGlow,
      },
    }));

    // Log detections for debugging with detailed analysis
    if (prohibitedItems.length > 0) {
      console.log('🚨 Object detection - Items detected:', prohibitedItems);
      console.log('📊 Detailed Analysis:', {
        brightness: Math.round(avgBrightness),
        motion: motionDetected,
        colorVariance: Math.round(colorVariance),
        colors: {
          red: Math.round(avgRed),
          green: Math.round(avgGreen),
          blue: Math.round(avgBlue)
        },
        flags: {
          isPhoneLike,
          isLaptopLike,
          isScreenGlow,
          isBookLike
        }
      });
    }
  }, [disabled]);

  // Start detection
  useEffect(() => {
    if (!disabled) {
      initializeCamera();
      
      // Start frame analysis
      intervalRef.current = setInterval(analyzeFrame, 1000); // Analyze every second
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, [disabled, initializeCamera, analyzeFrame]);

  return {
    detectionData,
    prohibitedItems: detectionData.prohibitedItems,
    itemDetectionCount: detectionData.itemDetectionCount,
    motionDetected: detectionData.motionDetected,
    brightnessLevel: detectionData.brightnessLevel,
    colorAnalysis: detectionData.colorAnalysis,
    videoRef,
    canvasRef,
    isActive: !disabled && !!streamRef.current,
  };
}
