// Assessment Criteria Types
export interface AssessmentScale {
  type: 'numeric' | 'categorical' | 'boolean';
  min?: number;
  max?: number;
  labels?: string[]; // For categorical scales like ['A1', 'A2', 'B1', 'B2'] or ['Poor', 'Good', 'Excellent']
  description?: string;
}

export interface AssessmentCriteria {
  id: string;
  name: string;
  description: string;
  scale: AssessmentScale;
  weight: number; // 1-100, percentage weight in overall score
  category: 'technical' | 'communication' | 'behavioral' | 'domain_specific' | 'custom';
  isRequired: boolean;
}

export interface AssessmentTemplate {
  id: string;
  name: string;
  description: string;
  roleType: 'technical' | 'non_technical' | 'sales' | 'language' | 'leadership' | 'customer_service' | 'marketing' | 'finance' | 'custom';
  criteria: AssessmentCriteria[];
  isDefault: boolean;
  isActive: boolean;
  organizationId?: string;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InterviewAssessmentConfig {
  usePrebuilt: boolean;
  selectedCriteriaIds: string[];
  customCriteria: AssessmentCriteria[];
  overallScoreCalculation: 'weighted_average' | 'custom';
  passingThreshold?: number;
}

// Assessment Result Types
export interface AssessmentResult {
  criteriaId: string;
  score: string | number | boolean;
  feedback: string;
  evidence: string;
}

export interface OverallAssessment {
  weighted_score: number;
  recommendation: 'hire' | 'not_hire' | 'maybe';
  summary: string;
}

export interface AssessmentFeedback {
  assessment_results: Record<string, AssessmentResult>;
  overall_assessment: OverallAssessment;
}

// Predefined Templates
export const ASSESSMENT_ROLE_TYPES = [
  { value: 'technical', label: 'Technical Roles', description: 'Software engineers, developers, architects' },
  { value: 'non_technical', label: 'Non-Technical Roles', description: 'General business roles' },
  { value: 'sales', label: 'Sales Roles', description: 'Sales representatives, account managers' },
  { value: 'language', label: 'Language Proficiency', description: 'Language assessment for any role' },
  { value: 'leadership', label: 'Leadership Roles', description: 'Managers, directors, executives' },
  { value: 'customer_service', label: 'Customer Service', description: 'Support, customer success roles' },
  { value: 'marketing', label: 'Marketing Roles', description: 'Marketing specialists, content creators' },
  { value: 'finance', label: 'Finance Roles', description: 'Accountants, financial analysts' },
  { value: 'custom', label: 'Custom Template', description: 'Create your own assessment criteria' },
] as const;

export const SCALE_TYPES = [
  { value: 'numeric', label: 'Numeric Scale', description: 'Rate from 1-10 or custom range' },
  { value: 'categorical', label: 'Categorical Scale', description: 'Predefined categories like A1, A2, B1, B2' },
  { value: 'boolean', label: 'Yes/No', description: 'Simple pass/fail criteria' },
] as const;

export const CRITERIA_CATEGORIES = [
  { value: 'technical', label: 'Technical Skills', description: 'Programming, system design, technical knowledge' },
  { value: 'communication', label: 'Communication', description: 'Verbal, written, presentation skills' },
  { value: 'behavioral', label: 'Behavioral', description: 'Teamwork, leadership, problem-solving approach' },
  { value: 'domain_specific', label: 'Domain Specific', description: 'Industry or role-specific knowledge' },
  { value: 'custom', label: 'Custom', description: 'Custom criteria specific to your needs' },
] as const;

// Common scale configurations with detailed context
export const COMMON_SCALES = {
  numeric_1_10: {
    type: 'numeric' as const,
    min: 1,
    max: 10,
    description: 'Rate from 1 (Poor) to 10 (Excellent)',
    context: 'Standard 10-point rating scale',
  },
  numeric_0_100: {
    type: 'numeric' as const,
    min: 0,
    max: 100,
    description: 'Percentage score from 0 to 100',
    context: 'Percentage-based scoring',
  },
  language_cefr: {
    type: 'categorical' as const,
    labels: ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'],
    description: 'Common European Framework of Reference for Languages (CEFR): A1=Beginner, A2=Elementary, B1=Intermediate, B2=Upper-Intermediate, C1=Advanced, C2=Proficient',
    context: 'European language proficiency standards',
  },
  performance_levels: {
    type: 'categorical' as const,
    labels: ['Poor', 'Below Average', 'Average', 'Good', 'Excellent'],
    description: 'Standard 5-level performance rating scale',
    context: 'General performance assessment',
  },
  technical_proficiency: {
    type: 'categorical' as const,
    labels: ['Novice', 'Beginner', 'Intermediate', 'Advanced', 'Expert'],
    description: 'Technical skill proficiency levels: Novice=No experience, Beginner=Basic knowledge, Intermediate=Practical experience, Advanced=Deep expertise, Expert=Industry leader',
    context: 'Technical skill assessment',
  },
  communication_quality: {
    type: 'categorical' as const,
    labels: ['Unclear', 'Basic', 'Clear', 'Articulate', 'Exceptional'],
    description: 'Communication effectiveness levels',
    context: 'Communication skill assessment',
  },
  pass_fail: {
    type: 'boolean' as const,
    description: 'Simple pass or fail assessment',
    context: 'Binary assessment',
  },
};

// Predefined scale options for UI
export const PREDEFINED_SCALE_OPTIONS = [
  {
    id: 'numeric_1_10',
    name: '1-10 Rating Scale',
    description: 'Standard 10-point scale (1=Poor, 10=Excellent)',
    scale: COMMON_SCALES.numeric_1_10,
  },
  {
    id: 'numeric_0_100',
    name: 'Percentage Scale (0-100)',
    description: 'Percentage-based scoring',
    scale: COMMON_SCALES.numeric_0_100,
  },
  {
    id: 'language_cefr',
    name: 'Language Proficiency (CEFR)',
    description: 'A1, A2, B1, B2, C1, C2 - European language standards',
    scale: COMMON_SCALES.language_cefr,
  },
  {
    id: 'technical_proficiency',
    name: 'Technical Proficiency',
    description: 'Novice, Beginner, Intermediate, Advanced, Expert',
    scale: COMMON_SCALES.technical_proficiency,
  },
  {
    id: 'communication_quality',
    name: 'Communication Quality',
    description: 'Unclear, Basic, Clear, Articulate, Exceptional',
    scale: COMMON_SCALES.communication_quality,
  },
  {
    id: 'performance_levels',
    name: 'Performance Levels',
    description: 'Poor, Below Average, Average, Good, Excellent',
    scale: COMMON_SCALES.performance_levels,
  },
  {
    id: 'pass_fail',
    name: 'Pass/Fail',
    description: 'Simple binary assessment',
    scale: COMMON_SCALES.pass_fail,
  },
  {
    id: 'custom',
    name: 'Custom Scale',
    description: 'Define your own scale labels',
    scale: null,
  },
] as const;
