'use client';

import * as React from 'react';

import { usePathname } from 'next/navigation';

import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { setCookie } from '@/utils/cookies';
import {
  BarChart3,
  Building2,
  ChevronLeft,
  ChevronRight,
  ChevronsUpDown,
  HeadphonesIcon,
  LayoutGrid,
  LogOut,
  MailIcon,
  Moon,
  Settings,
  Settings2,
  Sun,
} from 'lucide-react';
import { signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';

import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';
import { cn } from '@camped-ui/lib';

import SmallLogo from './logo';

interface Organization {
  id: string;
  organizationId: string;
  organization: {
    id: string;
    name: string;
    slug?: string;
    about?: string;
  };
  role: string;
}

interface SessionUser {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  membership?: any[];
}

interface SessionData {
  user?: SessionUser;
  organization?: Organization[];
}

type NavigationView = 'main' | 'applications' | 'organizations';

interface NavigationState {
  currentView: NavigationView;
  previousView?: NavigationView;
}

// Removed APPS array and getAppById function - no longer needed

interface AppSwitcherProps {
  defaultApp?: string;
  session?: SessionData;
  workspaces?: Organization[];
  currentTenantId?: string;
}

export function AppSwitcher({
  defaultApp = 'AcePrep',
  workspaces = [],
  currentTenantId,
}: AppSwitcherProps = {}) {
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();

  // Navigation state for the dropdown menu
  const [navigationState, setNavigationState] = React.useState<NavigationState>({
    currentView: 'main',
  });

  // Get current organization based on currentTenantId
  const currentOrganization = React.useMemo(() => {
    if (!currentTenantId || workspaces.length === 0) {
      return workspaces.length > 0 ? workspaces[0].organization : null;
    }

    const foundWorkspace = workspaces.find((workspace) => workspace.organizationId === currentTenantId);

    return foundWorkspace
      ? foundWorkspace.organization
      : workspaces.length > 0
      ? workspaces[0].organization
      : null;
  }, [currentTenantId, workspaces]);

  const handleSwitchOrganization = async (organization: Organization) => {
    try {
      // Set the organization cookie using the utility function
      setCookie('aceprepTenantId', organization.organizationId, {
        domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
      });

      // Redirect to dashboard after setting cookie
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Error switching organization:', error);
    }
  };

  const handleThemeToggle = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleSettings = () => {
    window.location.href = '/tenant/settings';
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/sign-in' });
  };

  // Navigation helper functions
  const navigateToView = (view: NavigationView) => {
    setNavigationState((prev) => ({
      currentView: view,
      previousView: prev.currentView,
    }));
  };

  const navigateBack = () => {
    setNavigationState((prev) => ({
      currentView: prev.previousView || 'main',
      previousView: undefined,
    }));
  };

  const resetNavigation = () => {
    setNavigationState({
      currentView: 'main',
    });
  };



  const [isOpen, setIsOpen] = React.useState(false);


  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button
          className="flex w-full items-center gap-2 rounded-lg text-left hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          onClick={() => {
            console.log('Button clicked');
            setIsOpen(!isOpen);
          }}
        >
          <SmallLogo />
          <div className="grid flex-1 text-left leading-tight group-data-[collapsible=icon]:hidden">
            <span className="truncate text-sm font-semibold uppercase">
              <span className="text-[#297FFF]">Flinkk</span>
              <span> </span>
              <span className="text-[#36C8AB]">Hire</span>
            </span>
            {currentOrganization?.name && (
              <span className="truncate text-xs text-muted-foreground">
                {currentOrganization.name}
              </span>
            )}
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 group-data-[collapsible=icon]:hidden" />
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[220px] rounded-lg p-2"
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <div className="space-y-1">
          {/* Organizations Section - only show if multiple workspaces */}
          {workspaces.length > 1 && (
            <>
              <div className="px-2 py-1.5">
                <span className="text-xs font-semibold text-muted-foreground">Organizations</span>
              </div>
              {workspaces.map((workspace) => (
                <div
                  key={workspace.organizationId}
                  className={cn(
                    'flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-secondary',
                    workspace.organizationId === currentTenantId && 'bg-secondary'
                  )}
                  onClick={() => {
                    handleSwitchOrganization(workspace);
                    setIsOpen(false);
                  }}
                >
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted">
                    <span className="text-xs font-medium">
                      {workspace.organization.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div className="flex flex-1 flex-col">
                    <span className="text-sm font-medium">{workspace.organization.name}</span>
                    {workspace.organization.slug && (
                      <span className="text-xs text-muted-foreground">{workspace.organization.slug}</span>
                    )}
                  </div>
                  {workspace.organizationId === currentTenantId && (
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  )}
                </div>
              ))}
              <div className="my-1 h-px bg-border" />
            </>
          )}

          {/* Theme Toggle */}
          <div
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-secondary"
            onClick={() => {
              handleThemeToggle();
              setIsOpen(false);
            }}
          >
            {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            <span>{theme === 'dark' ? 'Light Mode' : 'Dark Mode'}</span>
          </div>

          {/* Settings */}
          <div
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-secondary"
            onClick={() => {
              handleSettings();
              setIsOpen(false);
            }}
          >
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </div>

          <div className="my-1 h-px bg-border" />
          <div
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 text-red-600 hover:bg-secondary"
            onClick={() => {
              handleLogout();
              setIsOpen(false);
            }}
          >
            <LogOut className="h-4 w-4" />
            <span>Log out</span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
