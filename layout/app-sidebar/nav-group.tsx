'use client';

import React, { ReactNode } from 'react';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { Icon } from '@/icons';
import { ChevronRight } from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@camped-ui/collapsible';
import { cn } from '@camped-ui/lib';

export function NavGroup({ title, items }: any) {
  const pathName = usePathname() || '';
  return (
    <SidebarGroup>
      {title && <SidebarGroupLabel>{title}</SidebarGroupLabel>}
      <SidebarMenu>
        {items.map((item: any) => {
          const key = `${item.title}-${item.url}`;

          if (!item.items) return <SidebarMenuLink key={key} item={item} href={pathName} />;

          // if (state === "collapsed")
          //   return (
          //     <SidebarMenuCollapsedDropdown
          //       key={key}
          //       item={item}
          //       href={pathName}
          //     />
          //   );

          return <SidebarMenuCollapsible key={key} item={item} href={pathName} />;
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}

const NavBadge = ({ children }: { children: ReactNode }) => (
  <Badge className="rounded-full px-1 py-0 text-xs">{children}</Badge>
);

const SidebarMenuLink = ({ item, href }: { item: any; href: string }) => {
  const { setOpenMobile } = useSidebar();
  const isActive = checkIsActive(href, item);
  return (
    <SidebarMenuItem>
      <Link href={item.url} onClick={() => setOpenMobile(false)}>
        <SidebarMenuButton
          tooltip={item.title}
          className={cn(
            'flex cursor-pointer items-center hover:bg-[#EAEAEA] dark:hover:bg-[#202020]',
            isActive && 'bg-[#EAEAEA] text-accent-foreground dark:bg-[#202020]',
          )}
          onClick={() => setOpenMobile(false)}
        >
          <Icon name={item.icon} className="mr-2 h-5 w-5 shrink-0" />
          <p className="mt-0.5 min-w-0 flex-1 truncate text-[13px]">{item.title}</p>
          {item.badge && <NavBadge>{item.badge}</NavBadge>}
        </SidebarMenuButton>
      </Link>
    </SidebarMenuItem>
  );
};

const SidebarMenuCollapsible = ({ item, href }: { item: any; href: string }) => {
  const { setOpenMobile } = useSidebar();
  return (
    <SidebarMenuItem>
      <Collapsible defaultOpen={checkIsActive(href, item, true)} className="group/collapsible">
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title}>
            <Icon name={item.icon} className="mr-2 h-5 w-5" />
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub>
            {item.items.map((subItem: any) => (
              <SidebarMenuSubItem key={subItem.title}>
                <SidebarMenuSubButton asChild isActive={checkIsActive(href, subItem)}>
                  <Link
                    href={subItem.url}
                    onClick={() => setOpenMobile(false)}
                    className="cursor-pointer"
                  >
                    <Icon name={item.icon} className="mr-2 h-5 w-5" />
                    <span>{subItem.title}</span>
                    {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </Collapsible>
    </SidebarMenuItem>
  );
};

export function checkIsActive(href: string, item: any, mainNav = false) {
  // Remove query parameters from current path for comparison
  const currentPath = href.split('?')[0];
  const itemPath = item.url;

  // Exact match
  if (currentPath === itemPath) {
    return true;
  }

  // Check if current path starts with item path (for nested routes)
  // e.g., /sales/leads/123 should match /sales/leads
  if (currentPath.startsWith(itemPath) && currentPath.charAt(itemPath.length) === '/') {
    return true;
  }

  // Handle root paths (avoid matching "/" with everything)
  if (itemPath !== '/' && currentPath.startsWith(itemPath + '/')) {
    return true;
  }

  // Check if any child nav item is active (for collapsible menus)
  if (item?.items?.length > 0) {
    return item.items.some((childItem: any) => checkIsActive(href, childItem, false));
  }

  // For main navigation, check if we're in the same top-level section
  if (mainNav) {
    const currentSegments = currentPath.split('/').filter(Boolean);
    const itemSegments = itemPath.split('/').filter(Boolean);

    // Compare first non-empty segment
    if (currentSegments.length > 0 && itemSegments.length > 0) {
      return currentSegments[0] === itemSegments[0];
    }
  }

  return false;
}
