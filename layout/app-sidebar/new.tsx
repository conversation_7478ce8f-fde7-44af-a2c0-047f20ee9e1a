import Link from 'next/link';

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar';

import { Button } from '@camped-ui/button';

import { AppSwitcher } from './app-switcher';
import { NavGroup } from './nav-group';
import { NavUser } from './nav-user';

export const AppSidebar = ({
  data,
  defaultApp,
  user,
  session,
  workspaces,
  currentTenantId,
}: any) => {
  console.log({ data });
  return (
    <Sidebar collapsible="icon" variant="inset">
      <SidebarHeader>
        <AppSwitcher
          defaultApp={defaultApp}
          session={session}
          workspaces={workspaces}
          currentTenantId={currentTenantId}
        />
      </SidebarHeader>
      <SidebarContent>
        {data.map((item: any) => (
          <NavGroup key={item.title} {...item} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        {user ? (
          <NavUser user={user} />
        ) : (
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        )}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
