'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { ChevronsUpDown, HelpCircle, LogOut, Settings, UserIcon } from 'lucide-react';
import { signOut } from 'next-auth/react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';

interface NavUserProps {
  user?: {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}

export function NavUser({ user }: NavUserProps) {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const [isOpen, setIsOpen] = React.useState(false);

  const handleLogout = async () => {
    signOut({ callbackUrl: '/sign-in' });
    setIsOpen(false);
  };

  const handleProfileClick = () => {
    setIsOpen(false);
    router.push('/profile');
  };

  const getInitials = (name?: string | null) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };



  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button
          className="flex w-full items-center gap-2 rounded-lg p-2 text-left hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          onClick={() => {
            console.log('NavUser button clicked, current state:', isOpen);
            setIsOpen(!isOpen);
          }}
        >
          <Avatar className="h-8 w-8 rounded-lg">
            <AvatarImage src={user?.image || '/avatars/user.png'} alt={user?.name || 'User'} />
            <AvatarFallback className="rounded-lg">{getInitials(user?.name)}</AvatarFallback>
          </Avatar>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{user?.name || 'User'}</span>
            <span className="truncate text-xs text-muted-foreground">
              {user?.email || '<EMAIL>'}
            </span>
          </div>
          <ChevronsUpDown className="ml-auto size-4" />
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[220px] rounded-lg p-2"
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <div className="space-y-1">
          <div
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-secondary"
            onClick={handleProfileClick}
          >
            <UserIcon className="h-4 w-4" />
            <span>Profile</span>
          </div>
          <div className="my-1 h-px bg-border" />
          <div
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 text-red-600 hover:bg-secondary"
            onClick={() => {
              console.log('Logout clicked');
              handleLogout();
            }}
          >
            <LogOut className="h-4 w-4" />
            <span>Log out</span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
