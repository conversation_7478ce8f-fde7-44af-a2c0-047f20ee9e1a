'use client';

import { useSidebar } from '@/components/ui/sidebar';
import { Typography } from '@/components/ui/typography';
import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';
import { cn } from '@camped-ui/lib';

interface GlobalPanelProps {
  title: string;
  children: React.ReactNode;
}

export const GlobalPanel = ({ title, children }: GlobalPanelProps) => {
  const { toggleSidebar } = useSidebar();
  return (
    <div className="flex h-full flex-col">
      <div
        className={cn(
          'sticky top-0 z-[15] flex items-center justify-between gap-1.5 border-b border-[#E7E7E7] bg-background p-2 px-3 transition-colors md:min-h-12 dark:border-[#252525]',
        )}
      >
        <div className="flex items-center gap-2">
          <Typography type="h4">{title}</Typography>
        </div>
      </div>
      <div className="scrollbar flex-1 space-y-4 overflow-y-auto p-4">{children}</div>
    </div>
  );
};
