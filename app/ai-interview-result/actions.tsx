'use server';

import { getSignedUrl } from '@/pages/api/gcp-bucket';
import { listFolderContents } from '@/pages/api/upload-url';
import { db } from '@/prisma/db';
import { Storage } from '@google-cloud/storage';

const baseUrl = process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL;

export async function getResult(id) {
  if (!id) {
    throw new Error('Please provide an interview ID to view results');
  }

  try {
    const response = await db.careerPractice.findFirst({
      where: {
        references_id: id,
      },
      include: {
        eventDetails: true,
      },
    });

    console.log({ response });

    if (!response) {
      throw new Error(
        'Interview results not found. Please ensure you have the correct interview ID.',
      );
    }

    // Assuming careerPractice is your data structure containing conversation
    const conversation = response?.conversation;
    const organizationId = response?.eventDetails?.organizationId;

    // Use Promise.all to concurrently fetch the pre-signed URLs
    const mappedResponses = await Promise.all(
      (Array.isArray(conversation) ? conversation : [])?.map(async (response) => {
        try {
          if (
            typeof response === 'object' &&
            response !== null &&
            's3Id' in response &&
            response.s3Id
          ) {
            let videoUrl;
            if (response?.videoOrigin === 'GCP_BUCKET') {
              videoUrl = await getSignedUrl(response?.s3Id, organizationId);
            } else {
              videoUrl = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/${response.s3Id}`;
            }
            return {
              ...response,
              videoUrl,
            };
          } else {
            // Handle cases where response or response.s3Id is missing or falsy.
            // You can choose to return some default value or handle the error accordingly.
            if (typeof response === 'object' && response !== null) {
              return {
                ...response,
                videoUrl: null, // or any default value
              };
            } else {
              return {
                videoUrl: null,
              };
            }
          }
        } catch (error) {
          // Handle errors that occur while fetching the pre-signed URL.
          // You can choose to throw the error, log it, or handle it as needed.
          console.error(`Error fetching pre-signed URL for response:`, error);
          throw error; // Optionally rethrow the error if needed.
        }
      }),
    );
    // Check if this is an AI interview (no room_name in conversation)
    const isAiInterview = !response?.conversation?.[0]?.room_name;

    let separatedVideos: any = {
      screen: [],
      webcam: [],
    };

    if (isAiInterview) {
      // For AI interviews, fetch HLS recordings from GCP bucket
      try {
        const organizationId = response?.eventDetails?.organizationId;
        const videoId = id; // Use the CareerPractice ID as the video identifier

        // Initialize storage client for GCP bucket
        const storage = new Storage({
          projectId: process.env.NEXT_PUBLIC_GCP_PROJECT_ID,
          credentials: {
            private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
            client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
            client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
          },
        });

        // Force use of 'aceprep' bucket for AI interviews
        const bucketName = 'aceprep';

        // Check for available video files in the interview-ai folder
        const [files] = await storage.bucket(bucketName).getFiles({
          prefix: `interview-ai/${videoId}`,
        });

        // Check for HLS playlist file
        const m3u8File = files.find((file: any) => file.name.endsWith('.m3u8'));

        if (m3u8File) {
          try {
            // Generate signed URL for the HLS playlist using the API endpoint
            const hlsPlaylistUrl = `/api/hls-playlist?practiceId=${encodeURIComponent(videoId)}&organizationId=${organizationId || ''}`;

            // For AI interviews, we'll put the HLS playlist in the webcam array
            // This follows the pattern where webcam recordings are the main video
            separatedVideos.webcam.push(hlsPlaylistUrl);
          } catch (error) {
            console.error('Error generating HLS playlist URL:', error);
          }
        }

        // Check for converted MP4 file
        const mp4File = files.find((file: any) => file.name.endsWith('.mp4'));

        if (mp4File) {
          try {
            // Generate signed URL for the MP4 file
            const mp4SignedUrl = await storage
              .bucket(bucketName)
              .file(mp4File.name)
              .getSignedUrl({
                version: 'v4',
                action: 'read',
                expires: Date.now() + 15 * 60 * 1000, // 15 minutes
              });

            // Add MP4 to screen array as an alternative format
            separatedVideos.screen.push(mp4SignedUrl[0]);
          } catch (error) {
            console.error('Error generating MP4 signed URL:', error);
          }
        }
      } catch (error) {
        console.error('Error fetching AI interview recordings:', error);
      }
    } else {
      // For regular interviews, use existing S3 logic
      const Video_recordings: any = await listFolderContents(id);

      Video_recordings?.Contents?.forEach((recording) => {
        if (recording.Key.includes('screen')) {
          separatedVideos.screen.push(`${baseUrl}/${recording.Key}`);
        } else if (recording.Key.includes('webcam')) {
          separatedVideos.webcam.push(`${baseUrl}/${recording.Key}`);
        }
      });
    }
    return {
      result: {
        ...response,
        conversation: mappedResponses,
        videoRecordings: separatedVideos,
      },
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to fetch interview results. Please try again later.');
  }
}
