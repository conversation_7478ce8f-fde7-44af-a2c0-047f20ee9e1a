import { cookies } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';

import { EnhancedDashboard } from '@/components/dashboard/enhanced-dashboard';
import { HomeLayoutScreen } from '@/components/wrapper-screen/home/<USER>';
import { GlobalPanel } from '@/layout/global-panel';
import { authOptions } from '@/lib/auth';
import AceprepHome from '@/public/aceprep_home.png';
import home from '@/public/home.png';
import { getTotalInterviewCount, getTotalInterviewathonCount } from '@/services/apicall';
import { getServerSession } from 'next-auth';

import { Button } from '@camped-ui/button';

export default async function Layout({
  topCard,
  pendingTable,
  completedTable,
  interviewathonStats,
  interviewathonCompletedTable,
  leaderboard,
  activeStudent,
}) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get('aceprepTenantId')?.value ?? session?.memberships?.[0]?.organizationId;

  const currentOrg: any =
    session?.memberships?.find((item: any) => item?.organizationId === tenantId) ?? {};

  const isInterviewathon = currentOrg?.organization?.type?.toLowerCase() === 'institution';
  let interviewCount;
  if (!isInterviewathon) {
    interviewCount = await getTotalInterviewCount(tenantId);
  } else {
    interviewCount = await getTotalInterviewathonCount(tenantId);
  }
  if (interviewCount <= 0) {
    return (
      <div className="flex h-full w-full flex-1 flex-col p-4">
        <div className="mt-8 flex h-[650px] rounded-md">
          <div className="flex flex-col px-4 md:px-28">
            <br />
            <h3 className="mt-4 text-lg font-semibold">
              Overview of {isInterviewathon ? 'Interviewathon' : 'Interview'}
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Welcome to {isInterviewathon ? 'Aceprep' : 'Hire'}! To get started, create your first
              {isInterviewathon ? ' interviewathon' : ' interview'}. By doing so, you&apos;ll gain
              access to insights and analytics that can help you understand performance and make
              informed decisions.
            </p>
            <Image src={isInterviewathon ? AceprepHome : home} alt="no result" className="mt-4" />
            <br />
            <Button size="sm" className="max-w-[170px] self-center">
              <Link href={`/${isInterviewathon ? 'interviewathon' : 'interviews'}/create`}>
                Create {isInterviewathon ? 'Interviewathon' : 'Interview'}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <GlobalPanel title="Dashboard">
      <EnhancedDashboard organizationId={tenantId} />
    </GlobalPanel>
  );
}
