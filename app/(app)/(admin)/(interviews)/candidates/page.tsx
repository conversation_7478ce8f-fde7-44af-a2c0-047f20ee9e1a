import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { CandidateTable } from '@/components/data-table/candidate-list/data-table';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getCandidatesList, getEventList } from '@/services/apicall';

export type DocumentsPageProps = {
  searchParams?: {
    page?: string;
    per_page?: string;
    eventName?: string;
  };
};
export default async function ListEvent({ searchParams = {} }: DocumentsPageProps) {
  const userId = cookies().get('aceprepUserId')?.value;
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const event = searchParams?.eventName;

  const [membersList, eventList] = await Promise.all([
    getCandidatesList({
      organizationId: tenantId,
      role: 'CANDIDATE',
      userId: userId,
      searchParams,
    }),
    getEventList({
      tenantId,
      screen: 'interview',
      searchParams,
    }),
  ]);

  const eventId =
    Array.isArray(event?.split('.')) || !event ? event?.split('.') : [event?.split('.')];

  return (
    <CampedTableProvider>
      <CandidateTable
        eventList={eventList?.items}
        event={eventId}
        tasksPromise={{
          data: membersList,
        }}
      />
    </CampedTableProvider>
  );
}
