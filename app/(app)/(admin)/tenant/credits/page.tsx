import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { CreditsUsageScreen } from '@/components/wrapper-screen/credits-usage-screen';
import { getCandidatesCount } from '@/services/apicall';
import { getOrganizationById } from '@/services/apicall';

export default async function CreditsPage() {
  const userId = cookies().get('aceprepUserId')?.value;
  const organizationId = cookies().get('aceprepTenantId')?.value;

  if (!organizationId || !userId) {
    return redirect('/404');
  }

  const [organization, candidatePercentage] = await Promise.all([
    getOrganizationById(organizationId, userId),
    getCandidatesCount({ tenantId: organizationId }),
  ]);

  return (
    <CreditsUsageScreen
      organization={organization}
      candidatePercentage={candidatePercentage?.count}
    />
  );
}
