import { NextResponse } from 'next/server';
import { Storage } from '@google-cloud/storage';
import { db } from '@/prisma/db';
import { getHLSPlaylistSignedUrl } from '@/pages/api/gcp-bucket';

export const maxDuration = 60;

/**
 * API endpoint to fetch AI interview recordings
 * Returns HLS playlist URL and individual segment URLs for playback
 */
export async function GET(request: Request, { params }: { params: { id: string } }) {
  const { id } = params;

  try {
    // Get the AI interview (CareerPractice) with related data
    const aiInterview = await db.careerPractice.findUnique({
      where: { id },
      include: {
        eventDetails: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!aiInterview) {
      return NextResponse.json({ error: 'AI Interview not found' }, { status: 404 });
    }

    // Check if this is actually an AI interview (no room_name in conversation)
    const isAiInterview = !aiInterview.conversation?.[0]?.room_name;
    if (!isAiInterview) {
      return NextResponse.json({ error: 'This is not an AI interview' }, { status: 400 });
    }

    const organizationId = aiInterview.eventDetails?.organizationId;
    const videoId = id; // Use the CareerPractice ID as the video identifier

    console.log('🎥 Fetching AI Interview Recordings:', {
      aiInterviewId: id,
      videoId: videoId,
      organizationId: organizationId,
      expectedVideoPath: `interview-ai/${videoId}.m3u8`,
    });

    // Initialize storage client for GCP bucket
    const storage = new Storage({
      projectId: process.env.NEXT_PUBLIC_GCP_PROJECT_ID,
      credentials: {
        private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
        client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
        client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
      },
    });

    // Force use of 'aceprep' bucket for AI interviews
    const bucketName = 'aceprep';

    console.log('🪣 Using bucket:', bucketName);

    // Check for available video files in the interview-ai folder
    const [files] = await storage.bucket(bucketName).getFiles({
      prefix: `interview-ai/${videoId}`,
    });

    console.log('📂 Found files:', files.map((f: any) => f.name));

    const recordings = {
      screen: [],
      webcam: [],
    };

    // Check for HLS playlist file
    const m3u8File = files.find((file: any) => file.name.endsWith('.m3u8'));
    
    if (m3u8File) {
      try {
        // Generate signed URL for the HLS playlist
        const hlsPlaylistUrl = await getHLSPlaylistSignedUrl(videoId, organizationId);
        
        console.log('📋 HLS playlist found:', m3u8File.name);
        console.log('🔗 HLS playlist URL:', hlsPlaylistUrl);
        
        // For AI interviews, we'll put the HLS playlist in the webcam array
        // This follows the pattern where webcam recordings are the main video
        recordings.webcam.push(hlsPlaylistUrl);
      } catch (error) {
        console.error('Error generating HLS playlist URL:', error);
      }
    }

    // Check for converted MP4 file
    const mp4File = files.find((file: any) => file.name.endsWith('.mp4'));
    
    if (mp4File) {
      try {
        // Generate signed URL for the MP4 file
        const mp4SignedUrl = await storage
          .bucket(bucketName)
          .file(mp4File.name)
          .getSignedUrl({
            version: 'v4',
            action: 'read',
            expires: Date.now() + 15 * 60 * 1000, // 15 minutes
          });

        console.log('📹 MP4 file found:', mp4File.name);
        console.log('🔗 MP4 URL:', mp4SignedUrl[0]);
        
        // Add MP4 to screen array as an alternative format
        recordings.screen.push(mp4SignedUrl[0]);
      } catch (error) {
        console.error('Error generating MP4 signed URL:', error);
      }
    }

    // Check for individual .ts segment files (fallback)
    const tsFiles = files.filter((file: any) =>
      file.name.endsWith('.ts') && file.name.includes(`${videoId}_`)
    );

    if (tsFiles.length > 0 && recordings.webcam.length === 0 && recordings.screen.length === 0) {
      console.log('📹 Found .ts segments as fallback:', tsFiles.length);
      
      // Sort by segment number
      tsFiles.sort((a: any, b: any) => {
        const aNum = parseInt(a.name.match(/_(\d+)\.ts$/)?.[1] || '0');
        const bNum = parseInt(b.name.match(/_(\d+)\.ts$/)?.[1] || '0');
        return aNum - bNum;
      });

      // Generate signed URLs for the first few segments as a fallback
      const maxSegments = Math.min(3, tsFiles.length); // Limit to first 3 segments
      for (let i = 0; i < maxSegments; i++) {
        try {
          const segmentSignedUrl = await storage
            .bucket(bucketName)
            .file(tsFiles[i].name)
            .getSignedUrl({
              version: 'v4',
              action: 'read',
              expires: Date.now() + 15 * 60 * 1000, // 15 minutes
            });

          recordings.webcam.push(segmentSignedUrl[0]);
        } catch (error) {
          console.error('Error generating segment signed URL:', error);
        }
      }
    }

    console.log('✅ AI Interview recordings prepared:', {
      screenRecordings: recordings.screen.length,
      webcamRecordings: recordings.webcam.length,
      totalFiles: files.length,
    });

    return NextResponse.json(
      {
        recordings,
        metadata: {
          videoId,
          organizationId,
          totalFiles: files.length,
          hasHLS: !!m3u8File,
          hasMP4: !!mp4File,
          tsSegments: tsFiles.length,
        },
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error fetching AI interview recordings:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch AI interview recordings',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
