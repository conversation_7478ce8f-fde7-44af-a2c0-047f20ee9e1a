import { NextResponse } from 'next/server';

import InviteCandidate from '@/emails/inviteCandidate';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { getEmailTemplateByType } from '@/services/apicall';
import { render } from '@react-email/render';
import { getToken } from 'next-auth/jwt';

export const maxDuration = 299;

const generateEmailUrl = (
  baseUrl: string,
  userEmail: string,
  token: string,
  careerResponse: any,
) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/interview/onboarding?id=${careerResponse.id}`,
  )}`;
};

export async function POST(request: any) {
  const input = await request.json();

  try {
    const { users, eventId, isInvite } = input;

    const token = await getToken({ req: request });

    if (!token?.email || !eventId) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }

    const eventDetails: any = await db.eventDetails.findUnique({
      where: {
        id: eventId,
      },
    });

    if (!eventDetails?.organizationId && !eventDetails?.name && !eventDetails) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }
    const emailTemplate = await getEmailTemplateByType(
      'invite_candidate',
      eventDetails?.organizationId,
    );
    const linkValidity = eventDetails?.timing?.linkValidity;
    const eventName = eventDetails?.name;
    const organization = await db.organization.findUnique({
      where: {
        id: eventDetails?.organizationId,
      },
    });

    if (!organization?.name) {
      return NextResponse.json({
        message: 'Not Authorized to make this call',
        status: 400,
      });
    }
    const invitedUsers = await Promise.all(
      users?.map(async (user) => {
        try {
          const careerResponse = await db.careerPractice.findUnique({
            where: { id: user?.id },
          });
          if ((careerResponse as any)?.timing?.inviteTime && isInvite) return null;
          if (!(careerResponse as any)?.timing?.inviteTime && !isInvite) return null;

          const emailTemplateProps: any = {
            testUrl: `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/interview/onboarding?id=${careerResponse?.id}`,
            organization: organization?.name || '',
            isPlacement: eventDetails?.isPlacement,
            emailIdentifier: organization?.mailIdentifier,
            eventName: eventDetails?.name,
            linkValidity: eventDetails?.timing?.linkValidity,
            emailContent: emailTemplate?.emailContent,
          };

          const inviteMail = render(InviteCandidate(emailTemplateProps), {
            pretty: true,
          });
          const subject = emailTemplate?.emailSubject?.replaceAll(
            '{{organization}}',
            organization?.name,
          );
          const mailSendPayload = {
            to: user?.user?.email,
            subject:
              subject?.replaceAll('{{eventName}}', eventName) ??
              `${organization?.name} - ${eventName}`,
            html: inviteMail,
          };

          if (organization?.mailIdentifier) {
            (mailSendPayload as any).cc = [organization?.mailIdentifier];
          }
          const mailResponse = await campedMailer.send(mailSendPayload);

          if (mailResponse?.MessageId) {
            const timing = {
              ...(careerResponse as any)?.timing,
            };
            if (!(careerResponse as any)?.timing?.inviteTime) {
              timing.inviteTime = new Date();
            } else {
              timing.emailResentTime = new Date();
            }
            await db.careerPractice.update({
              data: {
                timing,
              },
              where: {
                id: careerResponse?.id,
              },
            });
            return { ...mailResponse, email: user?.user?.email, id: user?.id };
          }
          return { email: user?.user?.email, id: user?.id };
        } catch (error) {
          console.log('Error inviting candidate', { error });
          return { email: user?.user?.email, id: user?.id };
        }
      }),
    );

    const errorInvites = invitedUsers?.filter((item) => item)?.filter((item) => !item?.MessageId);
    if (errorInvites?.length > 0) {
      return NextResponse.json({
        message: 'Failed to invite candidate.',
        errorInvites,
        status: 400,
      });
    }
    return NextResponse.json({
      message: 'Candidate invite successful',
      status: 200,
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({
      message: 'Internal Server Error',
      status: 500,
    });
  }
}

const platformName = process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? 'Flinkk - Hire' : 'AcePrep';

function text({ url, organization }: { url: string; organization: string }) {
  return `Invitation for Interview Test with ${organization} on ${platformName}\n${url}\n\n`;
}
