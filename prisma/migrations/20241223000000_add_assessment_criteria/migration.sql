-- Create<PERSON><PERSON>
CREATE TYPE "AssessmentRoleType" AS ENUM ('TECHNICAL', 'NON_TECHNICAL', 'SALES', 'LANGUAGE', 'LEADERSHIP', 'CUSTOMER_SERVICE', 'MARKETING', 'FINANCE', 'CUSTOM');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AssessmentScaleType" AS ENUM ('NUMERIC', 'CATE<PERSON>ORICAL', 'BOOLEAN');

-- CreateTable
CREATE TABLE "assessment_templates" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "roleType" "AssessmentRoleType" NOT NULL,
    "criteria" JSONB NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "organizationId" TEXT,
    "createdById" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "assessment_templates_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "event_details" ADD COLUMN     "assessmentCriteria" JSONB,
ADD COLUMN     "assessmentTemplateId" TEXT;

-- AddForeignKey
ALTER TABLE "assessment_templates" ADD CONSTRAINT "assessment_templates_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessment_templates" ADD CONSTRAINT "assessment_templates_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "event_details" ADD CONSTRAINT "event_details_assessmentTemplateId_fkey" FOREIGN KEY ("assessmentTemplateId") REFERENCES "assessment_templates"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert default assessment templates
INSERT INTO "assessment_templates" ("id", "name", "description", "roleType", "criteria", "isDefault", "isActive", "createdAt", "updatedAt") VALUES
(
    'template_technical_001',
    'Technical Roles Assessment',
    'Comprehensive assessment for software engineers, developers, and technical roles',
    'TECHNICAL',
    '[
        {
            "id": "tech_problem_solving",
            "name": "Problem Solving",
            "description": "Ability to break down complex problems and find effective solutions",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "technical",
            "isRequired": true
        },
        {
            "id": "tech_knowledge",
            "name": "Technical Knowledge",
            "description": "Depth of knowledge in relevant technologies and frameworks",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "technical",
            "isRequired": true
        },
        {
            "id": "code_quality",
            "name": "Code Quality & Best Practices",
            "description": "Understanding of clean code, design patterns, and best practices",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "technical",
            "isRequired": true
        },
        {
            "id": "system_design",
            "name": "System Design Thinking",
            "description": "Ability to design scalable and maintainable systems",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "technical",
            "isRequired": false
        },
        {
            "id": "communication",
            "name": "Technical Communication",
            "description": "Ability to explain technical concepts clearly",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "communication",
            "isRequired": true
        }
    ]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'template_language_001',
    'Language Proficiency Assessment',
    'Comprehensive language assessment based on CEFR standards',
    'LANGUAGE',
    '[
        {
            "id": "speaking_fluency",
            "name": "Speaking Fluency",
            "description": "Ability to speak smoothly and naturally without hesitation",
            "scale": {
                "type": "categorical",
                "labels": ["A1", "A2", "B1", "B2", "C1", "C2"],
                "description": "Common European Framework of Reference for Languages (CEFR): A1=Beginner, A2=Elementary, B1=Intermediate, B2=Upper-Intermediate, C1=Advanced, C2=Proficient"
            },
            "weight": 25,
            "category": "communication",
            "isRequired": true
        },
        {
            "id": "grammar_accuracy",
            "name": "Grammar Accuracy",
            "description": "Correct use of grammar structures and syntax",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "communication",
            "isRequired": true
        },
        {
            "id": "vocabulary_range",
            "name": "Vocabulary Range",
            "description": "Breadth and appropriateness of vocabulary usage",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "communication",
            "isRequired": true
        },
        {
            "id": "pronunciation",
            "name": "Pronunciation",
            "description": "Clarity and accuracy of pronunciation",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "communication",
            "isRequired": true
        },
        {
            "id": "comprehension",
            "name": "Listening Comprehension",
            "description": "Ability to understand spoken language and respond appropriately",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "communication",
            "isRequired": true
        }
    ]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'template_sales_001',
    'Sales Roles Assessment',
    'Assessment for sales representatives, account managers, and business development roles',
    'SALES',
    '[
        {
            "id": "negotiation_skills",
            "name": "Negotiation Skills",
            "description": "Ability to negotiate effectively and reach win-win outcomes",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "domain_specific",
            "isRequired": true
        },
        {
            "id": "relationship_building",
            "name": "Customer Relationship Building",
            "description": "Ability to build rapport and maintain long-term customer relationships",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "product_knowledge",
            "name": "Product Knowledge",
            "description": "Understanding of products/services and ability to communicate value",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "domain_specific",
            "isRequired": true
        },
        {
            "id": "persuasion_ability",
            "name": "Persuasion & Influence",
            "description": "Ability to persuade and influence customer decisions",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "objection_handling",
            "name": "Objection Handling",
            "description": "Skill in addressing customer concerns and objections",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "domain_specific",
            "isRequired": true
        }
    ]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'template_leadership_001',
    'Leadership Assessment',
    'Assessment for managers, directors, and leadership positions',
    'LEADERSHIP',
    '[
        {
            "id": "team_management",
            "name": "Team Management",
            "description": "Ability to lead, motivate, and manage team members effectively",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "strategic_thinking",
            "name": "Strategic Thinking",
            "description": "Ability to think long-term and develop strategic plans",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "decision_making",
            "name": "Decision Making",
            "description": "Quality of decision-making process and outcomes",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "conflict_resolution",
            "name": "Conflict Resolution",
            "description": "Ability to resolve conflicts and manage difficult situations",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "vision_communication",
            "name": "Vision & Communication",
            "description": "Ability to articulate vision and communicate effectively with stakeholders",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "communication",
            "isRequired": true
        }
    ]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    'template_non_technical_001',
    'Non-Technical Roles Assessment',
    'General assessment for business roles and non-technical positions',
    'NON_TECHNICAL',
    '[
        {
            "id": "communication_skills",
            "name": "Communication Skills",
            "description": "Verbal and written communication effectiveness",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "communication",
            "isRequired": true
        },
        {
            "id": "analytical_thinking",
            "name": "Analytical Thinking",
            "description": "Ability to analyze information and solve problems",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 25,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "teamwork",
            "name": "Teamwork & Collaboration",
            "description": "Ability to work effectively in team environments",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 20,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "adaptability",
            "name": "Adaptability",
            "description": "Flexibility and ability to adapt to changing situations",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "behavioral",
            "isRequired": true
        },
        {
            "id": "initiative",
            "name": "Initiative & Proactivity",
            "description": "Self-motivation and ability to take initiative",
            "scale": {
                "type": "numeric",
                "min": 1,
                "max": 10,
                "description": "Rate from 1 (Poor) to 10 (Excellent)"
            },
            "weight": 15,
            "category": "behavioral",
            "isRequired": true
        }
    ]'::jsonb,
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);
