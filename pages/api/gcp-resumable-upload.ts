import { NextApiRequest, NextApiResponse } from 'next';

import { Storage } from '@google-cloud/storage';

// In-memory store for upload URLs (in production, use Redis or database)
const uploadUrlStore = new Map<string, string>();

// Helper function to read request body as string (for JSON)
async function getBodyAsString(req: NextApiRequest): Promise<string> {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', (chunk) => {
      body += chunk.toString();
    });
    req.on('end', () => {
      resolve(body);
    });
    req.on('error', (error) => {
      reject(error);
    });
  });
}

// Helper function to read request body as buffer (for binary data)
async function getBodyAsBuffer(req: NextApiRequest): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    req.on('data', (chunk: Buffer) => {
      chunks.push(chunk);
    });
    req.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
    req.on('error', (error) => {
      reject(error);
    });
  });
}

// Create GCP storage client
const createStorageClient = () => {
  return new Storage({
    projectId: process.env.NEXT_PUBLIC_GCP_PROJECT_ID,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
  });
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Content-Type, Content-Range, X-Upload-Content-Length, X-Upload-Content-Type',
  );
  res.setHeader('Access-Control-Expose-Headers', 'Location, Range');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { action, uploadId, fileName } = req.query;
  const bucketName = process.env.NEXT_PUBLIC_GCP_BUCKET || 'aceprep';
  const folder = process.env.NEXT_PUBLIC_S3FOLDER || 'recordings-ai';

  // Debug logging
  console.log('GCP Resumable Upload API called:', {
    action,
    uploadId,
    fileName,
    method: req.method,
    bodyType: typeof req.body,
    bodyLength: req.body?.length || 0,
  });

  try {
    const storage = createStorageClient();

    if (action === 'initiate') {
      // For initiate, we need fileName from request body
      let fileNameFromBody = fileName;

      try {
        const bodyStr = await getBodyAsString(req);
        console.log('Raw body received:', bodyStr);

        if (bodyStr) {
          const body = JSON.parse(bodyStr);
          fileNameFromBody = body.fileName || fileName;
          console.log('Parsed fileName:', fileNameFromBody);
        }
      } catch (error) {
        console.error('Error parsing request body:', error);
        // Fallback to query param
        fileNameFromBody = fileName;
      }

      // Ensure we have a valid filename
      if (!fileNameFromBody || fileNameFromBody === 'undefined') {
        console.error('Invalid fileName:', fileNameFromBody);
        return res.status(400).json({ error: 'fileName is required and cannot be undefined' });
      }

      const file = storage.bucket(bucketName).file(`${folder}/${fileNameFromBody}`);
      return await initiateResumableUpload(req, res, file);
    } else if (action === 'upload' && uploadId) {
      return await uploadChunk(req, res, uploadId as string);
    } else if (action === 'status' && uploadId) {
      return await getUploadStatus(req, res, uploadId as string);
    } else {
      return res.status(400).json({ error: 'Invalid action or missing parameters' });
    }
  } catch (error) {
    console.error('Resumable upload error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function initiateResumableUpload(req: NextApiRequest, res: NextApiResponse, file: any) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const contentLength = req.headers['x-upload-content-length'];
    const contentType = req.headers['x-upload-content-type'] || 'video/mp4';

    // Generate resumable upload URL using the correct method name
    const [uploadUrl] = await file.createResumableUpload({
      metadata: {
        contentType: contentType,
        ...(contentLength && { size: contentLength }),
      },
    });

    // Extract upload ID from the URL for tracking
    const uploadId = uploadUrl.split('upload_id=')[1]?.split('&')[0];

    // Store the mapping between upload ID and upload URL
    if (uploadId) {
      uploadUrlStore.set(uploadId, uploadUrl);
    }

    // Return only uploadId - chunks go through server proxy
    return res.status(200).json({
      uploadId,
      success: true,
    });
  } catch (error) {
    console.error('Error initiating resumable upload:', error);
    return res.status(500).json({ error: 'Failed to initiate upload' });
  }
}

async function uploadChunk(req: NextApiRequest, res: NextApiResponse, uploadId: string) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const contentRange = req.headers['content-range'];
    const contentLength = req.headers['content-length'];

    if (!contentRange) {
      return res.status(400).json({ error: 'Content-Range header required' });
    }

    // Get the upload URL from our store
    const uploadUrl = uploadUrlStore.get(uploadId);
    if (!uploadUrl) {
      return res.status(400).json({ error: 'Invalid upload ID or upload session expired' });
    }

    // Get raw body for upload
    const rawBody = await getBodyAsBuffer(req);

    // Forward the chunk to GCP
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Range': contentRange,
        'Content-Length': contentLength || rawBody.length.toString(),
        'Content-Type': 'video/mp4',
      },
      body: rawBody,
    });

    // Forward GCP response
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    Object.entries(responseHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });

    if (response.status === 308) {
      // Upload incomplete, return range
      return res.status(308).end();
    } else if (response.status >= 200 && response.status < 300) {
      // Upload complete
      const responseData = await response.text();
      return res.status(response.status).send(responseData);
    } else {
      // Error
      const errorData = await response.text();
      return res.status(response.status).send(errorData);
    }
  } catch (error) {
    console.error('Error uploading chunk:', error);
    return res.status(500).json({ error: 'Failed to upload chunk' });
  }
}

async function getUploadStatus(req: NextApiRequest, res: NextApiResponse, uploadId: string) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the upload URL from our store
    const uploadUrl = uploadUrlStore.get(uploadId);
    if (!uploadUrl) {
      return res.status(400).json({ error: 'Invalid upload ID or upload session expired' });
    }

    // Send empty PUT request to get upload status
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Range': 'bytes */*',
      },
    });

    const range = response.headers.get('range');

    return res.status(response.status).json({
      status: response.status,
      range: range,
      complete: response.status >= 200 && response.status < 300,
    });
  } catch (error) {
    console.error('Error getting upload status:', error);
    return res.status(500).json({ error: 'Failed to get upload status' });
  }
}

export const config = {
  api: {
    bodyParser: false, // We handle parsing manually based on action
    responseLimit: false, // Disable response size limit
  },
  // Vercel-specific configuration
  maxDuration: 300, // 5 minutes timeout for long uploads
};
