import { db } from '@/prisma/db';

const handler = async (req: any, res: any) => {
  const { query } = req;
  
  if (!query.organizationId) {
    return res.status(404).json({ error: 'Organization not found.' });
  }

  // Demo mode for testing
  if (query.organizationId === 'demo-org-id') {
    const mockResponse = {
      topMetrics: {
        activeRounds: 8,
        completedInterviews: query.filter === 'today' ? 24 : query.filter === 'thisWeek' ? 156 : 487,
        scheduledInterviews: query.filter === 'today' ? 12 : query.filter === 'thisWeek' ? 45 : 123,
        aiInterviews: query.filter === 'today' ? 156 : query.filter === 'thisWeek' ? 892 : 2456,
        successRate: query.filter === 'today' ? 87 : query.filter === 'thisWeek' ? 85 : 82
      },
      interviewTypes: {
        regular: {
          total: 1247,
          breakdown: {
            COMPLETED: 1089,
            PARTIALLY_COMPLETED: 98,
            NOT_STARTED: 60
          }
        },
        ai: {
          total: 892,
          breakdown: {
            COMPLETED: 756,
            PARTIALLY_COMPLETED: 124,
            NOT_STARTED: 12
          }
        },
        video: {
          todayCount: 24,
          weekCount: 156,
          breakdown: {
            PENDING: 89,
            COMPLETED: 65,
            CANCELLED: 2
          }
        }
      },
      recentActivity: [
        // Invited (NOT_STARTED)
        {
          id: 'career-practice-1',
          candidateName: 'Alice Johnson',
          interviewName: 'Frontend Developer Interview',
          type: 'Regular Interview',
          status: 'NOT_STARTED',
          score: null,
          completedTime: null,
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-1'
        },
        {
          id: 'career-practice-2',
          candidateName: 'Bob Wilson',
          interviewName: 'Data Scientist Position',
          type: 'AI Interview',
          status: 'NOT_STARTED',
          score: null,
          completedTime: null,
          createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-2'
        },
        {
          id: 'career-practice-3',
          candidateName: 'Carol Davis',
          interviewName: 'UX Designer Role',
          type: 'Regular Interview',
          status: 'NOT_STARTED',
          score: null,
          completedTime: null,
          createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-3'
        },
        // Ongoing (PARTIALLY_COMPLETED)
        {
          id: 'career-practice-4',
          candidateName: 'David Brown',
          interviewName: 'Backend Engineer Interview',
          type: 'Regular Interview',
          status: 'PARTIALLY_COMPLETED',
          score: null,
          completedTime: null,
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          eventId: 'event-4'
        },
        {
          id: 'career-practice-5',
          candidateName: 'Eva Martinez',
          interviewName: 'Product Manager Assessment',
          type: 'AI Interview',
          status: 'PARTIALLY_COMPLETED',
          score: null,
          completedTime: null,
          createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          eventId: 'event-5'
        },
        // Completed (COMPLETED)
        {
          id: 'career-practice-6',
          candidateName: 'John Doe',
          interviewName: 'Software Engineer Interview',
          type: 'AI Interview',
          status: 'COMPLETED',
          score: 85,
          completedTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-6'
        },
        {
          id: 'career-practice-7',
          candidateName: 'Jane Smith',
          interviewName: 'DevOps Engineer Role',
          type: 'Regular Interview',
          status: 'COMPLETED',
          score: 78,
          completedTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-7'
        },
        {
          id: 'career-practice-8',
          candidateName: 'Mike Johnson',
          interviewName: 'Technical Assessment',
          type: 'AI Interview',
          status: 'COMPLETED',
          score: 92,
          completedTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          eventId: 'event-8'
        }
      ],
      scheduledInterviews: {
        upcoming: [
          {
            id: 'video-1',
            candidateName: 'Sarah Connor',
            interviewName: 'Senior Developer Interview',
            role: 'Senior Software Engineer',
            scheduleTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
            meetingType: 'Technical Interview',
            eventId: 'event-9',
            careerPracticeId: 'career-practice-9'
          },
          {
            id: 'video-2',
            candidateName: 'Tom Hardy',
            interviewName: 'Product Manager Interview',
            role: 'Product Manager',
            scheduleTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
            meetingType: 'Behavioral Interview',
            eventId: 'event-10',
            careerPracticeId: 'career-practice-10'
          },
          {
            id: 'video-3',
            candidateName: 'Emma Watson',
            interviewName: 'Design Lead Interview',
            role: 'Design Lead',
            scheduleTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
            meetingType: 'Portfolio Review',
            eventId: 'event-11',
            careerPracticeId: 'career-practice-11'
          }
        ],
        completed: [
          {
            id: 'video-4',
            candidateName: 'Chris Evans',
            interviewName: 'DevOps Engineer Interview',
            role: 'DevOps Engineer',
            scheduleTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
            meetingType: 'Technical Interview',
            eventId: 'event-12',
            careerPracticeId: 'career-practice-12'
          },
          {
            id: 'video-5',
            candidateName: 'Scarlett Johansson',
            interviewName: 'Marketing Manager Interview',
            role: 'Marketing Manager',
            scheduleTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
            meetingType: 'Strategy Discussion',
            eventId: 'event-13',
            careerPracticeId: 'career-practice-13'
          }
        ]
      },
      summary: {
        totalInterviews: 2139,
        completedInterviews: 1845,
        successRate: 87,
        timeFilter: query.filter || 'today'
      }
    };
    return res.status(200).json(mockResponse);
  }

  const organizationId = query.organizationId;
  const timeFilter = query.filter || 'today';
  
  try {
    // Get date ranges for filtering
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    let dateFilter = {};
    if (timeFilter === 'today') {
      dateFilter = { gte: today };
    } else if (timeFilter === 'thisWeek') {
      dateFilter = { gte: thisWeek };
    } else if (timeFilter === 'thisMonth') {
      dateFilter = { gte: thisMonth };
    }

    // Parallel queries for better performance
    const [
      activeRounds,
      completedInterviews,
      scheduledInterviews,
      aiInterviews,
      totalInterviews,
      completedForSuccessRate,
      videoMeetingsToday,
      videoMeetingsThisWeek,
      recentActivity,
      upcomingScheduledInterviews,
      completedScheduledInterviews
    ] = await Promise.all([
      // Active interview rounds
      db.eventDetails.count({
        where: {
          organizationId,
          status: 'ACTIVE'
        }
      }),
      
      // Completed interviews (filtered by time)
      db.careerPractice.count({
        where: {
          eventDetails: { organizationId },
          completedTime: dateFilter,
          interviewStatus: 'COMPLETED'
        }
      }),

      // Scheduled interviews (filtered by time)
      db.videoCallInterview.count({
        where: {
          organizationId,
          scheduleTime: dateFilter,
          meetingStatus: 'PENDING'
        }
      }),

      // AI interviews (filtered by time)
      db.careerPractice.count({
        where: {
          eventDetails: {
            organizationId,
            isAiQuestion: true
          },
          createdAt: dateFilter
        }
      }),
      
      // Total interviews for success rate calculation
      db.careerPractice.count({
        where: {
          eventDetails: { organizationId },
          createdAt: dateFilter
        }
      }),
      
      // Completed interviews for success rate calculation
      db.careerPractice.count({
        where: {
          eventDetails: { organizationId },
          interviewStatus: 'COMPLETED',
          completedTime: dateFilter
        }
      }),
      
      // Video meetings today
      db.videoCallInterview.count({
        where: {
          organizationId,
          scheduleTime: {
            gte: today,
            lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // Video meetings this week
      db.videoCallInterview.count({
        where: {
          organizationId,
          scheduleTime: { gte: thisWeek }
        }
      }),
      
      // Get balanced recent activity (10 from each status)
      Promise.all([
        // Invited (NOT_STARTED) - Top 10
        db.careerPractice.findMany({
          where: {
            eventDetails: { organizationId },
            interviewStatus: 'NOT_STARTED'
          },
          select: {
            id: true,
            interviewStatus: true,
            completedTime: true,
            createdAt: true,
            finalScore: true,
            user: {
              select: {
                email: true,
                userProfile: {
                  select: {
                    fullName: true
                  }
                }
              }
            },
            eventDetails: {
              select: {
                id: true,
                name: true,
                isAiQuestion: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }),
        // Ongoing (PARTIALLY_COMPLETED) - Top 10
        db.careerPractice.findMany({
          where: {
            eventDetails: { organizationId },
            interviewStatus: 'PARTIALLY_COMPLETED'
          },
          select: {
            id: true,
            interviewStatus: true,
            completedTime: true,
            createdAt: true,
            finalScore: true,
            user: {
              select: {
                email: true,
                userProfile: {
                  select: {
                    fullName: true
                  }
                }
              }
            },
            eventDetails: {
              select: {
                id: true,
                name: true,
                isAiQuestion: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }),
        // Completed (COMPLETED) - Top 10
        db.careerPractice.findMany({
          where: {
            eventDetails: { organizationId },
            interviewStatus: 'COMPLETED'
          },
          select: {
            id: true,
            interviewStatus: true,
            completedTime: true,
            createdAt: true,
            finalScore: true,
            user: {
              select: {
                email: true,
                userProfile: {
                  select: {
                    fullName: true
                  }
                }
              }
            },
            eventDetails: {
              select: {
                id: true,
                name: true,
                isAiQuestion: true
              }
            }
          },
          orderBy: {
            completedTime: 'desc'
          },
          take: 10
        })
      ]).then(([invited, ongoing, completed]) => [...invited, ...ongoing, ...completed]),

      // Upcoming scheduled interviews (next 5)
      db.videoCallInterview.findMany({
        where: {
          organizationId,
          scheduleTime: { gte: now },
          meetingStatus: 'PENDING'
        },
        select: {
          id: true,
          scheduleTime: true,
          meetingType: true,
          participants: true,
          eventDetails: {
            select: {
              id: true,
              name: true,
              role: true
            }
          },
          careerPractice: {
            select: {
              id: true,
              user: {
                select: {
                  email: true,
                  userProfile: {
                    select: {
                      fullName: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          scheduleTime: 'asc'
        },
        take: 5
      }),

      // Completed scheduled interviews (last 5)
      db.videoCallInterview.findMany({
        where: {
          organizationId,
          meetingStatus: 'COMPLETED'
        },
        select: {
          id: true,
          scheduleTime: true,
          meetingType: true,
          participants: true,
          eventDetails: {
            select: {
              id: true,
              name: true,
              role: true
            }
          },
          careerPractice: {
            select: {
              id: true,
              user: {
                select: {
                  email: true,
                  userProfile: {
                    select: {
                      fullName: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          scheduleTime: 'desc'
        },
        take: 5
      })
    ]);

    // Calculate success rate
    const successRate = totalInterviews > 0 ? Math.round((completedForSuccessRate / totalInterviews) * 100) : 0;

    // Get interview type breakdowns (filtered by time)
    const [regularInterviews, aiInterviewsBreakdown, videoMeetings] = await Promise.all([
      // Regular interviews breakdown
      db.careerPractice.groupBy({
        by: ['interviewStatus'],
        where: {
          eventDetails: {
            organizationId,
            isAiQuestion: false
          },
          createdAt: dateFilter
        },
        _count: true
      }),

      // AI interviews breakdown
      db.careerPractice.groupBy({
        by: ['interviewStatus'],
        where: {
          eventDetails: {
            organizationId,
            isAiQuestion: true
          },
          createdAt: dateFilter
        },
        _count: true
      }),

      // Video meetings breakdown
      db.videoCallInterview.groupBy({
        by: ['meetingStatus'],
        where: {
          organizationId,
          scheduleTime: dateFilter
        },
        _count: true
      })
    ]);

    // Format the response
    const response = {
      topMetrics: {
        activeRounds,
        completedInterviews,
        scheduledInterviews,
        aiInterviews,
        successRate
      },
      interviewTypes: {
        regular: {
          total: regularInterviews.reduce((sum, item) => sum + item._count, 0),
          breakdown: regularInterviews.reduce((acc, item) => {
            acc[item.interviewStatus] = item._count;
            return acc;
          }, {})
        },
        ai: {
          total: aiInterviewsBreakdown.reduce((sum, item) => sum + item._count, 0),
          breakdown: aiInterviewsBreakdown.reduce((acc, item) => {
            acc[item.interviewStatus] = item._count;
            return acc;
          }, {})
        },
        video: {
          todayCount: videoMeetingsToday,
          weekCount: videoMeetingsThisWeek,
          breakdown: videoMeetings.reduce((acc, item) => {
            acc[item.meetingStatus] = item._count;
            return acc;
          }, {})
        }
      },
      recentActivity: recentActivity.map(interview => ({
        id: interview.id,
        candidateName: interview.user?.userProfile?.fullName || interview.user?.email || 'Unknown',
        interviewName: interview.eventDetails?.name || 'Interview',
        type: interview.eventDetails?.isAiQuestion ? 'AI Interview' : 'Regular Interview',
        status: interview.interviewStatus,
        score: interview.finalScore,
        completedTime: interview.completedTime,
        createdAt: interview.createdAt,
        eventId: interview.eventDetails?.id
      })),
      scheduledInterviews: {
        upcoming: upcomingScheduledInterviews.map(interview => ({
          id: interview.id,
          candidateName: interview.careerPractice?.user?.userProfile?.fullName ||
                        interview.careerPractice?.user?.email ||
                        'Unknown',
          interviewName: interview.eventDetails?.name || 'Video Interview',
          role: interview.eventDetails?.role || 'N/A',
          scheduleTime: interview.scheduleTime,
          meetingType: interview.meetingType,
          eventId: interview.eventDetails?.id,
          careerPracticeId: interview.careerPractice?.id
        })),
        completed: completedScheduledInterviews.map(interview => ({
          id: interview.id,
          candidateName: interview.careerPractice?.user?.userProfile?.fullName ||
                        interview.careerPractice?.user?.email ||
                        'Unknown',
          interviewName: interview.eventDetails?.name || 'Video Interview',
          role: interview.eventDetails?.role || 'N/A',
          scheduleTime: interview.scheduleTime,
          meetingType: interview.meetingType,
          eventId: interview.eventDetails?.id,
          careerPracticeId: interview.careerPractice?.id
        }))
      },
      summary: {
        totalInterviews,
        completedInterviews,
        successRate,
        timeFilter
      }
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Enhanced metrics error:', error);
    return res.status(500).json({ error: 'Failed to fetch dashboard metrics' });
  }
};

export default handler;
