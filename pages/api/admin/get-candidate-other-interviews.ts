import { db } from '@/prisma/db';

const handler = async (req: any, res: any) => {
  const { candidateId, organizationId, currentInterviewId, membershipId, role } = req.query;

  if (!candidateId || !organizationId) {
    return res.status(400).json({ message: 'Missing required parameters' });
  }

  try {
    // Build where clause for organization access control
    let where: any = {
      userId: candidateId,
      id: {
        not: currentInterviewId, // Exclude current interview
      },
      eventDetails: {
        organizationId: organizationId,
      },
    };

    // Apply membership-based filtering for non-admin/owner roles
    if (membershipId && !['admin', 'owner']?.includes(role?.toLowerCase())) {
      where.eventDetails = {
        ...where.eventDetails,
        MembershipOnEventDetails: {
          some: {
            membershipId,
          },
        },
      };
    }

    // Fetch career practice interviews
    const careerPracticeInterviews = await db.careerPractice.findMany({
      where,
      select: {
        id: true,
        role: true,
        level: true,
        event: true,
        finalScore: true,
        resumeScore: true,
        interviewStatus: true,
        completedTime: true,
        createdAt: true,
        feedback: true, // Get the entire feedback JSON object
        eventDetails: {
          select: {
            id: true,
            name: true,
            role: true,
            level: true,
            isAiQuestion: true,
            isPlacement: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Build where clause for video meetings
    let videoMeetingWhere: any = {
      userId: candidateId,
      organizationId: organizationId,
      id: {
        not: currentInterviewId, // Exclude current interview if it's a video meeting
      },
    };

    // Apply membership-based filtering for video meetings
    if (membershipId && !['admin', 'owner']?.includes(role?.toLowerCase())) {
      videoMeetingWhere.eventDetails = {
        MembershipOnEventDetails: {
          some: {
            membershipId,
          },
        },
      };
    }

    // Fetch video meetings/scheduled interviews
    const videoMeetings = await db.videoCallInterview.findMany({
      where: videoMeetingWhere,
      select: {
        id: true,
        meetingStatus: true,
        scheduleTime: true,
        meetingType: true,
        createdAt: true,
        feedback: true,
        careerPractice: {
          select: {
            id: true,
            role: true,
            level: true,
            event: true,
            finalScore: true,
            resumeScore: true,
            interviewStatus: true,
            completedTime: true,
            feedback: true,
          },
        },
        eventDetails: {
          select: {
            id: true,
            name: true,
            role: true,
            level: true,
            isAiQuestion: true,
            isPlacement: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Combine and format all interviews
    const allInterviews = [
      // Career practice interviews
      ...careerPracticeInterviews.map(interview => ({
        ...interview,
        type: 'career_practice',
        interviewType: interview.eventDetails?.isAiQuestion ? 'AI Interview' :
                      interview.eventDetails?.isPlacement ? 'Placement Interview' : 'Regular Interview',
      })),
      // Video meetings
      ...videoMeetings.map(meeting => ({
        id: meeting.id,
        role: meeting.careerPractice?.role || meeting.eventDetails?.role || 'N/A',
        level: meeting.careerPractice?.level || meeting.eventDetails?.level || 'N/A',
        event: meeting.eventDetails?.name || 'Video Meeting',
        finalScore: meeting.careerPractice?.finalScore,
        resumeScore: meeting.careerPractice?.resumeScore,
        interviewStatus: meeting.meetingStatus,
        completedTime: meeting.careerPractice?.completedTime,
        createdAt: meeting.createdAt,
        scheduleTime: meeting.scheduleTime,
        feedback: meeting.feedback || meeting.careerPractice?.feedback,
        eventDetails: meeting.eventDetails,
        type: 'video_meeting',
        interviewType: 'Video Meeting',
        meetingType: meeting.meetingType,
        careerPracticeId: meeting.careerPractice?.id,
      })),
    ];

    // Sort all interviews by creation date
    allInterviews.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return res.status(200).json({
      interviews: allInterviews,
    });
  } catch (error) {
    console.error('Error fetching candidate other interviews:', error);
    return res.status(500).json({
      message: 'Error fetching candidate interviews',
      error: error.message,
    });
  }
};

export default handler;
