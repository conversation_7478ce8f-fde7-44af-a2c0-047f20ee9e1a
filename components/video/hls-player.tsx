'use client';

import React, { useEffect, useRef, useState } from 'react';
import Hls from 'hls.js';
import { Card } from '@camped-ui/card';
import { Play } from 'lucide-react';

interface HLSPlayerProps {
  src: string;
  className?: string;
}

export const HLSPlayer: React.FC<HLSPlayerProps> = ({ src, className = '' }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const initializeHLS = () => {
    if (!videoRef.current || !src) return;

    setIsLoading(true);
    setError(null);

    if (hlsRef.current) {
      hlsRef.current.destroy();
    }

    if (Hls.isSupported()) {
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
      });

      hlsRef.current = hls;

      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('🎥 HLS Media attached');
      });

      hls.on(Hls.Events.MANIFEST_LOADED, (event, data) => {
        console.log(`🎥 HLS Manifest loaded: ${data.levels.length} levels`);
        setIsLoading(false);
      });

      hls.on(Hls.Events.LEVEL_LOADED, () => {
        setIsLoading(false);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error(`🎥 HLS Error: ${data.type} - ${data.details}`, data);
        if (data.fatal) {
          setError(`Failed to load video: ${data.details}`);
          setIsLoading(false);
        }
      });

      hls.attachMedia(videoRef.current);
      hls.loadSource(src);
    } else {
      // Fallback to native HLS (Safari)
      if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
        videoRef.current.src = src;
        videoRef.current.addEventListener('loadeddata', () => setIsLoading(false));
        videoRef.current.addEventListener('error', () => {
          setError('Failed to load video');
          setIsLoading(false);
        });
      } else {
        setError('HLS not supported in this browser');
        setIsLoading(false);
      }
    }
  };

  // Auto-initialize when component mounts or src changes
  useEffect(() => {
    if (src) {
      initializeHLS();
    }
  }, [src]);

  useEffect(() => {
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
      }
    };
  }, []);

  if (error) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
          <div className="text-center">
            <div className="mx-auto mb-2 h-12 w-12 rounded-full bg-red-100 p-3 dark:bg-red-900">
              <Play className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <p className="text-sm font-medium text-red-900 dark:text-red-100">Video Error</p>
            <p className="text-xs text-red-600 dark:text-red-400">{error}</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-black/50">
            <div className="text-center">
              <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
              <p className="text-sm text-white">Loading video...</p>
            </div>
          </div>
        )}

        <video
          ref={videoRef}
          controls
          className="w-full aspect-video bg-black rounded-lg"
          preload="metadata"
          onError={(e) => {
            console.error(`🎥 Video element error:`, e.currentTarget.error);
            setError('Video playback error');
            setIsLoading(false);
          }}
          onLoadStart={() => setIsLoading(true)}
          onCanPlay={() => setIsLoading(false)}
          style={{ maxHeight: '400px', maxWidth: '100%' }}
        />
      </div>
    </Card>
  );
};
