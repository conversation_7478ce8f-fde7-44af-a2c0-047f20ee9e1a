'use client';

import { useState } from 'react';
import Link from 'next/link';

import { Icon } from '@/icons';

import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

interface ActivityItem {
  id: string;
  candidateName: string;
  interviewName: string;
  type: string;
  status: string;
  score?: number;
  completedTime?: string;
  createdAt: string;
  eventId?: string;
}

interface RecentActivityProps {
  activities: ActivityItem[];
  isLoading?: boolean;
}

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'COMPLETED':
      return <Icon name="CheckCircle" className="h-4 w-4 text-green-500" />;
    case 'PARTIALLY_COMPLETED':
      return <Icon name="Clock" className="h-4 w-4 text-yellow-500" />;
    case 'NOT_STARTED':
      return <Icon name="Circle" className="h-4 w-4 text-gray-400" />;
    default:
      return <Icon name="Circle" className="h-4 w-4 text-gray-400" />;
  }
};

const TypeBadge = ({ type }: { type: string }) => {
  const getTypeStyle = (type: string) => {
    if (type.includes('AI')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    }
    if (type.includes('Video')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getTypeStyle(type)}`}>
      {type}
    </span>
  );
};

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return date.toLocaleDateString();
};

export const RecentActivity: React.FC<RecentActivityProps> = ({ activities, isLoading }) => {
  const [activeTab, setActiveTab] = useState<'invited' | 'ongoing' | 'completed'>('invited');

  // Categorize activities by status
  const categorizedActivities = {
    invited: activities?.filter(activity => activity.status === 'NOT_STARTED') || [],
    ongoing: activities?.filter(activity => activity.status === 'PARTIALLY_COMPLETED') || [],
    completed: activities?.filter(activity => activity.status === 'COMPLETED') || []
  };

  const tabs = [
    {
      key: 'invited' as const,
      label: 'Invited',
      count: categorizedActivities.invited.length,
      icon: 'Send',
      color: 'text-blue-600 border-blue-600'
    },
    {
      key: 'ongoing' as const,
      label: 'Ongoing',
      count: categorizedActivities.ongoing.length,
      icon: 'Clock',
      color: 'text-orange-600 border-orange-600'
    },
    {
      key: 'completed' as const,
      label: 'Completed',
      count: categorizedActivities.completed.length,
      icon: 'CheckCircle',
      color: 'text-green-600 border-green-600'
    }
  ];

  const currentActivities = categorizedActivities[activeTab];

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Activity" className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <div className="p-6 space-y-4">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="flex items-center space-x-4 animate-pulse">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-6 w-16 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="Activity" className="h-5 w-5" />
          Recent Activity
         
        </CardTitle>
      </CardHeader>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                activeTab === tab.key
                  ? `${tab.color} border-current`
                  : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon name={tab.icon} className="h-4 w-4" />
              {tab.label}
             
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[300px]">
        {currentActivities.length === 0 ? (
          <div className="p-6 text-center">
            <Icon name="FileX" className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <CardDescription>
              No {activeTab} interviews found
            </CardDescription>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {currentActivities.map((activity, index) => (
              <Link
                key={activity.id}
                href={`/interviews/${activity.eventId}/${activity.id}`}
                className="block hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="p-4 flex items-center space-x-4">
                  {/* Status Icon */}
                  <div className="flex-shrink-0">
                    <StatusIcon status={activity.status} />
                  </div>

                  {/* Main Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {activity.candidateName}
                      </p>
                      <TypeBadge type={activity.type} />
                    </div>

                    <p className="text-sm text-gray-500 truncate">
                      {activity.interviewName}
                    </p>

                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-xs text-gray-400">
                        {formatTimeAgo(activity.completedTime || activity.createdAt)}
                      </span>

                      {activity.score && (
                        <span className="text-xs font-medium text-blue-600">
                          Score: {activity.score}%
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Arrow */}
                  <div className="flex-shrink-0">
                    <Icon name="ChevronRight" className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>

      {/* View All Link */}
      <div className="p-4 border-t bg-gray-50">
        <Link
          href="/interviews"
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center gap-1"
        >
          View All Interviews
          <Icon name="ArrowRight" className="h-4 w-4" />
        </Link>
      </div>
    </Card>
  );
};
