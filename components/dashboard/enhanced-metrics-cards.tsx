'use client';

import { Icon } from '@/icons';

import { Card, CardDescription, CardTitle } from '@camped-ui/card';

interface MetricsData {
  activeRounds: number;
  completedInterviews: number;
  scheduledInterviews: number;
  aiInterviews: number;
  successRate: number;
}

interface EnhancedMetricsCardsProps {
  data: MetricsData;
  timeFilter: string;
  isLoading?: boolean;
}

const colorSchemes = [
  {
    gradient: 'from-blue-500/20 via-blue-400/10 to-transparent',
    border: 'border-blue-200/50 hover:border-blue-400/60',
    shadow: 'hover:shadow-blue-500/25',
    accent: 'bg-gradient-to-r from-blue-500 to-blue-600',
    glow: 'from-blue-400/30 to-blue-600/20',
    text: 'group-hover:text-blue-600',
    iconColor: 'text-blue-500',
  },
  {
    gradient: 'from-green-500/20 via-green-400/10 to-transparent',
    border: 'border-green-200/50 hover:border-green-400/60',
    shadow: 'hover:shadow-green-500/25',
    accent: 'bg-gradient-to-r from-green-500 to-green-600',
    glow: 'from-green-400/30 to-green-600/20',
    text: 'group-hover:text-green-600',
    iconColor: 'text-green-500',
  },
  {
    gradient: 'from-orange-500/20 via-orange-400/10 to-transparent',
    border: 'border-orange-200/50 hover:border-orange-400/60',
    shadow: 'hover:shadow-orange-500/25',
    accent: 'bg-gradient-to-r from-orange-500 to-orange-600',
    glow: 'from-orange-400/30 to-orange-600/20',
    text: 'group-hover:text-orange-600',
    iconColor: 'text-orange-500',
  },
  {
    gradient: 'from-purple-500/20 via-purple-400/10 to-transparent',
    border: 'border-purple-200/50 hover:border-purple-400/60',
    shadow: 'hover:shadow-purple-500/25',
    accent: 'bg-gradient-to-r from-purple-500 to-purple-600',
    glow: 'from-purple-400/30 to-purple-600/20',
    text: 'group-hover:text-purple-600',
    iconColor: 'text-purple-500',
  },
  {
    gradient: 'from-pink-500/20 via-pink-400/10 to-transparent',
    border: 'border-pink-200/50 hover:border-pink-400/60',
    shadow: 'hover:shadow-pink-500/25',
    accent: 'bg-gradient-to-r from-pink-500 to-pink-600',
    glow: 'from-pink-400/30 to-pink-600/20',
    text: 'group-hover:text-pink-600',
    iconColor: 'text-pink-500',
  },
];

export const EnhancedMetricsCards: React.FC<EnhancedMetricsCardsProps> = ({ data, timeFilter, isLoading }) => {
  // Dynamic labels based on time filter
  const getTimeLabel = () => {
    switch (timeFilter) {
      case 'today': return 'Today';
      case 'thisWeek': return 'This Week';
      case 'thisMonth': return 'This Month';
      default: return 'Today';
    }
  };

  const metrics = [
    {
      label: 'Active Interview Rounds',
      value: data?.activeRounds || 0,
      icon: 'CalendarRange',
      bottomLabel: 'Live Now',
      trend: data?.activeRounds > 0 ? `${data.activeRounds} Active` : undefined,
    },
    {
      label: `${getTimeLabel()}'s Completed`,
      value: data?.completedInterviews || 0,
      icon: 'CircleCheck',
      bottomLabel: 'Interviews',
      trend: data?.completedInterviews > 0 ? 'Great Progress!' : undefined,
    },
    {
      label: `Scheduled ${getTimeLabel()}`,
      value: data?.scheduledInterviews || 0,
      icon: 'Clock',
      bottomLabel: 'Upcoming',
      trend: data?.scheduledInterviews > 0 ? 'Ready to go' : undefined,
    },
    {
      label: 'AI Interviews',
      value: data?.aiInterviews || 0,
      icon: 'Bot',
      bottomLabel: getTimeLabel(),
      trend: data?.aiInterviews > 0 ? 'AI Powered' : undefined,
    },
    {
      label: 'Success Rate',
      value: `${data?.successRate || 0}%`,
      icon: 'TrendingUp',
      bottomLabel: 'Completion',
      trend: data?.successRate >= 80 ? 'Excellent!' : data?.successRate >= 60 ? 'Good' : 'Needs Improvement',
    },
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
        {[...Array(5)].map((_, index) => (
          <Card key={index} className="p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="h-8 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded"></div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
      {metrics.map((meta, index) => {
        const scheme = colorSchemes[index % colorSchemes.length];

        return (
          <Card
            key={index}
            className={`group relative overflow-hidden border-2 bg-white/90 backdrop-blur-md shadow-lg transition-all duration-500 hover:shadow-2xl hover:-translate-y-1 dark:bg-base-900/90 ${scheme.border} ${scheme.shadow}`}
          >
            {/* Dynamic gradient background */}
            <div className={`absolute inset-0 bg-gradient-to-br ${scheme.gradient} transition-opacity duration-500 opacity-100`} />

            {/* Animated glow effect */}
            <div className={`absolute -inset-1 bg-gradient-to-r ${scheme.glow} rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-tilt`} />

            <div className="relative p-6 z-10">
              {/* Header with animated label */}
              <div className="mb-4">
                <CardTitle className="text-xs font-bold uppercase tracking-widest text-muted-foreground/60 transition-all duration-300 group-hover:text-foreground group-hover:tracking-wide">
                  {meta.label}
                </CardTitle>
                {meta.trend && (
                  <div className="mt-2 flex items-center gap-2">
                    <div className="relative">
                      <div className="h-3 w-3 rounded-full bg-emerald-500 animate-ping absolute"></div>
                      <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
                    </div>
                    <span className="text-xs font-bold text-emerald-600 dark:text-emerald-400 animate-pulse">{meta.trend}</span>
                  </div>
                )}
              </div>

              {/* Massive value with dramatic styling */}
              <div className="mb-6 relative">
                <CardTitle className={`text-4xl md:text-5xl font-black tracking-tighter text-foreground transition-all duration-500 group-hover:scale-110 transform-gpu ${scheme.text}`}>
                  {meta.value}
                </CardTitle>
                {/* Value glow effect */}
                <div className={`absolute inset-0 text-4xl md:text-5xl font-black tracking-tighter bg-gradient-to-r ${scheme.accent} bg-clip-text text-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-30 blur-sm`}>
                  {meta.value}
                </div>
              </div>

              {/* Bottom section with description and icon */}
              <div className="flex items-end justify-between">
                <CardDescription className="text-sm font-semibold leading-relaxed text-muted-foreground transition-all duration-300 group-hover:text-foreground group-hover:font-bold">
                  {meta.bottomLabel}
                </CardDescription>

                {/* Icon in bottom right corner */}
                {meta.icon && (
                  <Icon
                    name={meta.icon}
                    className={`h-8 w-8 transition-all duration-500 group-hover:scale-110 group-hover:rotate-12 ${scheme.iconColor} opacity-80 group-hover:opacity-100`}
                  />
                )}
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};
