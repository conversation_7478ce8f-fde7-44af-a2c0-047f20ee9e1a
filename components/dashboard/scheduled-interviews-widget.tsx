'use client';

import { useState } from 'react';
import Link from 'next/link';

import { Icon } from '@/icons';

import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

interface ScheduledInterview {
  id: string;
  candidateName: string;
  interviewName: string;
  role: string;
  scheduleTime: string;
  meetingType: string;
  eventId?: string;
  careerPracticeId?: string;
}

interface ScheduledInterviewsData {
  upcoming: ScheduledInterview[];
  completed: ScheduledInterview[];
}

interface ScheduledInterviewsWidgetProps {
  data: ScheduledInterviewsData;
  isLoading?: boolean;
}

const formatScheduleTime = (dateString: string, isUpcoming: boolean = true) => {
  const date = new Date(dateString);
  const now = new Date();
  
  if (isUpcoming) {
    const diffInMinutes = Math.floor((date.getTime() - now.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `In ${diffInMinutes}m`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `In ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `In ${diffInDays}d`;
    
    return date.toLocaleDateString();
  } else {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  }
};

const InterviewCard = ({ interview, isUpcoming }: { interview: ScheduledInterview; isUpcoming: boolean }) => {
  const linkHref = interview.eventId && interview.careerPracticeId 
    ? `/interviews/${interview.eventId}/${interview.careerPracticeId}`
    : '#';

  return (
    <Link href={linkHref} className="block hover:bg-gray-50 transition-colors duration-200">
      <div className="p-3 flex items-center space-x-3">
        {/* Status Icon */}
        <div className="flex-shrink-0">
          <Icon 
            name={isUpcoming ? "Calendar" : "CheckCircle"} 
            className={`h-4 w-4 ${isUpcoming ? 'text-blue-500' : 'text-green-500'}`} 
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <p className="text-sm font-medium text-gray-900 truncate">
              {interview.candidateName}
            </p>
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {interview.meetingType}
            </span>
          </div>
          
          <p className="text-sm text-gray-500 truncate">
            {interview.role} • {interview.interviewName}
          </p>
          
          <div className="flex items-center gap-4 mt-1">
            <span className={`text-xs font-medium ${
              isUpcoming ? 'text-blue-600' : 'text-gray-400'
            }`}>
              {formatScheduleTime(interview.scheduleTime, isUpcoming)}
            </span>
          </div>
        </div>

        {/* Action Arrow */}
        <div className="flex-shrink-0">
          <Icon name="ChevronRight" className="h-4 w-4 text-gray-400" />
        </div>
      </div>
    </Link>
  );
};

export const ScheduledInterviewsWidget: React.FC<ScheduledInterviewsWidgetProps> = ({ data, isLoading }) => {
  const [activeTab, setActiveTab] = useState<'upcoming' | 'completed'>('upcoming');

  const tabs = [
    { 
      key: 'upcoming' as const, 
      label: 'Upcoming', 
      count: data?.upcoming?.length || 0,
      icon: 'Calendar',
      color: 'text-blue-600 border-blue-600'
    },
    { 
      key: 'completed' as const, 
      label: 'Completed', 
      count: data?.completed?.length || 0,
      icon: 'CheckCircle',
      color: 'text-green-600 border-green-600'
    }
  ];

  const currentInterviews = data?.[activeTab] || [];

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon name="Video" className="h-5 w-5" />
            Scheduled Interviews
          </CardTitle>
        </CardHeader>
        <div className="p-6 space-y-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex items-center space-x-4 animate-pulse">
              <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="Video" className="h-5 w-5" />
          Scheduled Interviews
         
        </CardTitle>
      </CardHeader>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-3 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                activeTab === tab.key
                  ? `${tab.color} border-current`
                  : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon name={tab.icon} className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[250px]">
        {currentInterviews.length === 0 ? (
          <div className="p-6 text-center">
            <Icon name="Calendar" className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <CardDescription>
              No {activeTab} scheduled interviews
            </CardDescription>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {currentInterviews.map((interview) => (
              <InterviewCard 
                key={interview.id} 
                interview={interview} 
                isUpcoming={activeTab === 'upcoming'}
              />
            ))}
          </div>
        )}
      </div>

      {/* View All Link */}
      <div className="p-4 border-t bg-gray-50">
        <Link 
          href="/video-interviews" 
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center gap-1"
        >
          View All Scheduled Interviews
          <Icon name="ArrowRight" className="h-4 w-4" />
        </Link>
      </div>
    </Card>
  );
};
