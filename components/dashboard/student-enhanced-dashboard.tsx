'use client';

import { useEffect, useState } from 'react';

import { EnhancedMetricsCards } from './enhanced-metrics-cards';

import { getActivityGraph } from '@/services/apicall';

import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

interface StudentDashboardProps {
  userId: string;
}

interface StudentMetrics {
  activeRounds: number;
  todayCompleted: number;
  scheduledToday: number;
  aiInterviewsThisMonth: number;
  successRate: number;
}

export const StudentEnhancedDashboard: React.FC<StudentDashboardProps> = ({ userId }) => {
  const [metrics, setMetrics] = useState<StudentMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStudentData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // For students, we'll use the existing activity graph API and transform the data
      const activityData = await getActivityGraph(userId);
      
      // Transform the data to match our enhanced metrics format
      const transformedMetrics: StudentMetrics = {
        activeRounds: 0, // Students don't have "active rounds" concept
        todayCompleted: 0, // We'll calculate this from activity data
        scheduledToday: 0, // Students don't typically see scheduled interviews
        aiInterviewsThisMonth: activityData?.mock_practice_count || 0,
        successRate: activityData?.current_streak_score || 0,
      };

      setMetrics(transformedMetrics);
    } catch (err) {
      console.error('Failed to fetch student dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchStudentData();
    }
  }, [userId]);

  if (error) {
    return (
      <Card className="p-6 text-center">
        <CardTitle className="text-red-600 mb-2">Error Loading Dashboard</CardTitle>
        <CardDescription>{error}</CardDescription>
        <button 
          onClick={fetchStudentData}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </Card>
    );
  }

  // For students, we'll show a simplified version focused on their practice and progress
  const studentMetrics = {
    activeRounds: 0, // Not applicable for students
    todayCompleted: metrics?.todayCompleted || 0,
    scheduledToday: 0, // Not applicable for students
    aiInterviewsThisMonth: metrics?.aiInterviewsThisMonth || 0,
    successRate: metrics?.successRate || 0,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold tracking-tight">🎯 Your Practice Dashboard</h1>
        <p className="text-muted-foreground">
          Track your interview preparation progress
        </p>
      </div>

      {/* Student-focused metrics */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Your Progress</h2>
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {/* Current Streak */}
          <Card className="p-4 text-center">
            <CardTitle className="text-2xl font-bold text-blue-600">
              {metrics?.successRate || 0}
            </CardTitle>
            <CardDescription>Current Streak</CardDescription>
          </Card>

          {/* Mock Interviews */}
          <Card className="p-4 text-center">
            <CardTitle className="text-2xl font-bold text-green-600">
              {metrics?.aiInterviewsThisMonth || 0}
            </CardTitle>
            <CardDescription>Mock Interviews</CardDescription>
          </Card>

          {/* Practice Sessions */}
          <Card className="p-4 text-center">
            <CardTitle className="text-2xl font-bold text-purple-600">
              {metrics?.todayCompleted || 0}
            </CardTitle>
            <CardDescription>Today's Practice</CardDescription>
          </Card>

          {/* Improvement Score */}
          <Card className="p-4 text-center">
            <CardTitle className="text-2xl font-bold text-orange-600">
              {metrics?.successRate || 0}%
            </CardTitle>
            <CardDescription>Success Rate</CardDescription>
          </Card>
        </div>
      </div>

      {/* Student Stats */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📊 Your Stats</CardTitle>
          </CardHeader>
          <div className="p-6 pt-0 space-y-4">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded animate-pulse"></div>
                ))}
              </div>
            ) : (
              <>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Practice Sessions</span>
                  <span className="font-semibold">{metrics?.aiInterviewsThisMonth || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Current Streak</span>
                  <span className="font-semibold text-blue-600">{metrics?.successRate || 0} days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">This Month</span>
                  <span className="font-semibold text-green-600">{metrics?.aiInterviewsThisMonth || 0} sessions</span>
                </div>
              </>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};
