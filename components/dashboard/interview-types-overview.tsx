'use client';

import { Icon } from '@/icons';

import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

interface InterviewTypeData {
  total: number;
  breakdown: {
    COMPLETED?: number;
    PARTIALLY_COMPLETED?: number;
    NOT_STARTED?: number;
    PENDING?: number;
    CANCELLED?: number;
  };
  todayCount?: number;
  weekCount?: number;
}

interface InterviewTypesData {
  regular: InterviewTypeData;
  ai: InterviewTypeData;
  video: InterviewTypeData;
}

interface InterviewTypesOverviewProps {
  data: InterviewTypesData;
  isLoading?: boolean;
}

const ProgressBar = ({ percentage, color = 'bg-blue-500' }: { percentage: number; color?: string }) => (
  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
    <div 
      className={`${color} h-2 rounded-full transition-all duration-500 ease-out`}
      style={{ width: `${Math.min(percentage, 100)}%` }}
    ></div>
  </div>
);

const StatusBadge = ({ status, count }: { status: string; count: number }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'PARTIALLY_COMPLETED':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'NOT_STARTED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'PENDING':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(status)}`}>
      {formatStatus(status)}: {count}
    </span>
  );
};

export const InterviewTypesOverview: React.FC<InterviewTypesOverviewProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  const interviewTypes = [
    {
      title: 'Regular Interviews',
      icon: 'FileText',
      data: data.regular,
      color: 'blue',
      gradient: 'from-blue-500/10 to-blue-600/5',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200',
    },
    {
      title: 'AI Interviews',
      icon: 'Bot',
      data: data.ai,
      color: 'purple',
      gradient: 'from-purple-500/10 to-purple-600/5',
      iconColor: 'text-purple-600',
      borderColor: 'border-purple-200',
    },
    {
      title: 'Video Meetings',
      icon: 'Video',
      data: data.video,
      color: 'green',
      gradient: 'from-green-500/10 to-green-600/5',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {interviewTypes.map((type, index) => {
        const total = type.data.total || (type.data.todayCount || 0) + (type.data.weekCount || 0);
        const completed = type.data.breakdown?.COMPLETED || 0;
        const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

        return (
          <Card key={index} className={`relative overflow-hidden border-2 ${type.borderColor} hover:shadow-lg transition-all duration-300`}>
            {/* Background gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${type.gradient}`} />
            
            <CardHeader className="relative z-10">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-bold flex items-center gap-2">
                  <Icon name={type.icon} className={`h-5 w-5 ${type.iconColor}`} />
                  {type.title}
                </CardTitle>
                <div className="text-right">
                  <div className="text-2xl font-bold">{total}</div>
                  <CardDescription className="text-xs">Total</CardDescription>
                </div>
              </div>
            </CardHeader>

            <div className="px-6 pb-6 relative z-10">
              {/* Completion Rate */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Completion Rate</span>
                  <span className="text-sm font-bold">{completionRate}%</span>
                </div>
                <ProgressBar 
                  percentage={completionRate} 
                  color={`bg-${type.color}-500`}
                />
              </div>

              {/* Status Breakdown */}
              <div className="space-y-2">
                <CardDescription className="text-xs font-medium uppercase tracking-wide">Status Breakdown</CardDescription>
                <div className="flex flex-wrap gap-1">
                  {Object.entries(type.data.breakdown || {}).map(([status, count]) => (
                    <StatusBadge key={status} status={status} count={count} />
                  ))}
                </div>
              </div>

              {/* Special metrics for video meetings */}
              {type.title === 'Video Meetings' && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-blue-600">{type.data.todayCount || 0}</div>
                      <CardDescription className="text-xs">Today</CardDescription>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">{type.data.weekCount || 0}</div>
                      <CardDescription className="text-xs">This Week</CardDescription>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        );
      })}
    </div>
  );
};
