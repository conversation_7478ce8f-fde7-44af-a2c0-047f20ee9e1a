'use client';

import { useEffect, useState } from 'react';

import { EnhancedMetricsCards } from './enhanced-metrics-cards';
import { InterviewTypesOverview } from './interview-types-overview';
import { RecentActivity } from './recent-activity';
import { ScheduledInterviewsWidget } from './scheduled-interviews-widget';

import { getEnhancedDashboardMetrics } from '@/services/apicall';

import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

interface EnhancedDashboardProps {
  organizationId: string;
  initialFilter?: string;
}

interface DashboardData {
  topMetrics: {
    activeRounds: number;
    completedInterviews: number;
    scheduledInterviews: number;
    aiInterviews: number;
    successRate: number;
  };
  interviewTypes: {
    regular: {
      total: number;
      breakdown: Record<string, number>;
    };
    ai: {
      total: number;
      breakdown: Record<string, number>;
    };
    video: {
      todayCount: number;
      weekCount: number;
      breakdown: Record<string, number>;
    };
  };
  recentActivity: Array<{
    id: string;
    candidateName: string;
    interviewName: string;
    type: string;
    status: string;
    score?: number;
    completedTime?: string;
    createdAt: string;
    eventId?: string;
  }>;
  scheduledInterviews: {
    upcoming: Array<{
      id: string;
      candidateName: string;
      interviewName: string;
      role: string;
      scheduleTime: string;
      meetingType: string;
      eventId?: string;
      careerPracticeId?: string;
    }>;
    completed: Array<{
      id: string;
      candidateName: string;
      interviewName: string;
      role: string;
      scheduleTime: string;
      meetingType: string;
      eventId?: string;
      careerPracticeId?: string;
    }>;
  };
  summary: {
    totalInterviews: number;
    completedInterviews: number;
    successRate: number;
    timeFilter: string;
  };
}

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ 
  organizationId, 
  initialFilter = 'today' 
}) => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeFilter, setTimeFilter] = useState(initialFilter);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async (filter: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await getEnhancedDashboardMetrics(organizationId, filter);
      setData(result);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (organizationId) {
      fetchDashboardData(timeFilter);
    }
  }, [organizationId, timeFilter]);

  const handleFilterChange = (newFilter: string) => {
    setTimeFilter(newFilter);
  };

  if (error) {
    return (
      <div className="p-6">
        <Card className="p-6 text-center">
          <CardTitle className="text-red-600 mb-2">Error Loading Dashboard</CardTitle>
          <CardDescription>{error}</CardDescription>
          <button 
            onClick={() => fetchDashboardData(timeFilter)}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Filter */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
         
        </div>
        
        <select
          value={timeFilter}
          onChange={(e) => handleFilterChange(e.target.value)}
          className="w-[180px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="today">Today</option>
          <option value="thisWeek">This Week</option>
          <option value="thisMonth">This Month</option>
        </select>
      </div>

      {/* Top Metrics Cards */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Key Metrics</h2>
        <EnhancedMetricsCards data={data?.topMetrics} timeFilter={timeFilter} isLoading={isLoading} />
      </div>

      {/* Interview Types Overview */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Interview Types Overview</h2>
        <InterviewTypesOverview data={data?.interviewTypes} isLoading={isLoading} />
      </div>

      {/* Bottom Section: Recent Activity, Scheduled Interviews, and Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity - Takes 1 column */}
        <div>
          <RecentActivity activities={data?.recentActivity || []} isLoading={isLoading} />
        </div>

        {/* Scheduled Interviews - Takes 1 column */}
        <div>
          <ScheduledInterviewsWidget data={data?.scheduledInterviews} isLoading={isLoading} />
        </div>

        {/* Summary Stats - Takes 1 column */}
        <div>
          {/* Quick Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">📊 Quick Summary</CardTitle>
            </CardHeader>
            <div className="p-6 pt-0 space-y-4">
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              ) : (
                <>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Interviews</span>
                    <span className="font-semibold">{data?.summary?.totalInterviews || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Completed</span>
                    <span className="font-semibold text-green-600">{data?.summary?.completedInterviews || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Success Rate</span>
                    <span className="font-semibold text-blue-600">{data?.summary?.successRate || 0}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Time Period</span>
                    <span className="font-semibold capitalize">{timeFilter.replace(/([A-Z])/g, ' $1').trim()}</span>
                  </div>
                </>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
