'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@/icons';
import { Button } from '@camped-ui/button';

interface ProctoringDashboardProps {
  proctoringData: {
    fullScreen: { status: string };
    tabFocus: { status: boolean };
    eyeTracking: {
      attentionScore: number;
      lookAwayCount: number;
      averageGazeDeviation: number;
    };
    emotionAnalysis: {
      stressLevel: number;
      suspiciousBehavior: boolean;
      emotionChanges: number;
    };
    objectDetection: {
      prohibitedItems: string[];
      itemDetectionCount: number;
    };
    multiPerson: {
      additionalPeopleDetected: boolean;
      personCount: number;
      violations: number;
    };
    voiceAnalysis: {
      suspiciousAudio: boolean;
      voicePatternChanges: number;
      backgroundNoise: number;
    };
  };
  onViolationAlert?: (violation: string, severity: 'low' | 'medium' | 'high' | 'critical') => void;
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
}

export const EnhancedProctoringDashboard: React.FC<ProctoringDashboardProps> = ({
  proctoringData,
  onViolationAlert,
  isMinimized = false,
  onToggleMinimize,
}) => {
  const [activeAlerts, setActiveAlerts] = useState<string[]>([]);
  const [overallRiskLevel, setOverallRiskLevel] = useState<'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'>('LOW');

  // Calculate overall risk level
  useEffect(() => {
    const calculateRiskLevel = () => {
      let riskScore = 0;

      // Eye tracking violations
      if (proctoringData.eyeTracking.attentionScore < 50) riskScore += 2;
      if (proctoringData.eyeTracking.lookAwayCount > 3) riskScore += 3;

      // Emotion analysis violations
      if (proctoringData.emotionAnalysis.stressLevel > 70) riskScore += 2;
      if (proctoringData.emotionAnalysis.suspiciousBehavior) riskScore += 4;

      // Object detection violations
      if (proctoringData.objectDetection.prohibitedItems.length > 0) riskScore += 5;

      // Multi-person violations
      if (proctoringData.multiPerson.additionalPeopleDetected) riskScore += 6;

      // Voice analysis violations
      if (proctoringData.voiceAnalysis.suspiciousAudio) riskScore += 3;
      if (proctoringData.voiceAnalysis.backgroundNoise > 50) riskScore += 2;

      // Tab/fullscreen violations
      if (!proctoringData.tabFocus.status) riskScore += 4;
      if (proctoringData.fullScreen.status !== 'on') riskScore += 3;

      // Determine risk level
      if (riskScore >= 15) return 'CRITICAL';
      if (riskScore >= 10) return 'HIGH';
      if (riskScore >= 5) return 'MEDIUM';
      return 'LOW';
    };

    const newRiskLevel = calculateRiskLevel();
    setOverallRiskLevel(newRiskLevel);

    // Trigger alerts for high-risk situations
    if (newRiskLevel === 'CRITICAL' || newRiskLevel === 'HIGH') {
      onViolationAlert?.('High risk behavior detected', newRiskLevel.toLowerCase() as any);
    }
  }, [proctoringData, onViolationAlert]);

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'CRITICAL': return 'text-red-600 bg-red-50 border-red-200';
      case 'HIGH': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getScoreColor = (score: number, reverse = false) => {
    if (reverse) {
      if (score >= 70) return 'text-red-600';
      if (score >= 40) return 'text-yellow-600';
      return 'text-green-600';
    } else {
      if (score >= 70) return 'text-green-600';
      if (score >= 40) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  if (isMinimized) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="fixed top-4 right-4 z-50"
      >
        <div className={`rounded-lg border-2 p-3 shadow-lg ${getRiskColor(overallRiskLevel)}`}>
          <div className="flex items-center gap-2">
            <div className="flex h-3 w-3 items-center justify-center">
              <div className={`h-2 w-2 rounded-full ${
                overallRiskLevel === 'CRITICAL' ? 'bg-red-500 animate-pulse' :
                overallRiskLevel === 'HIGH' ? 'bg-orange-500' :
                overallRiskLevel === 'MEDIUM' ? 'bg-yellow-500' : 'bg-green-500'
              }`} />
            </div>
            <span className="text-sm font-medium">{overallRiskLevel}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleMinimize}
              className="h-6 w-6 p-0"
            >
              <Icon name="Maximize2" className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed top-4 right-4 z-50 w-80 rounded-lg border bg-white shadow-xl"
    >
      {/* Header */}
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          <Icon name="Shield" className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold">Proctoring Monitor</h3>
        </div>
        <div className="flex items-center gap-2">
          <div className={`rounded-full px-2 py-1 text-xs font-medium border ${getRiskColor(overallRiskLevel)}`}>
            {overallRiskLevel}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleMinimize}
            className="h-6 w-6 p-0"
          >
            <Icon name="Minimize2" className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="p-4 space-y-4">
        {/* Eye Tracking */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Eye Tracking</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Attention:</span>
              <span className={getScoreColor(proctoringData.eyeTracking.attentionScore)}>
                {proctoringData.eyeTracking.attentionScore}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Look Away:</span>
              <span className={getScoreColor(proctoringData.eyeTracking.lookAwayCount, true)}>
                {proctoringData.eyeTracking.lookAwayCount}
              </span>
            </div>
          </div>
        </div>

        {/* Emotion Analysis */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Behavior Analysis</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Stress Level:</span>
              <span className={getScoreColor(proctoringData.emotionAnalysis.stressLevel, true)}>
                {proctoringData.emotionAnalysis.stressLevel}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Suspicious:</span>
              <span className={proctoringData.emotionAnalysis.suspiciousBehavior ? 'text-red-600' : 'text-green-600'}>
                {proctoringData.emotionAnalysis.suspiciousBehavior ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        {/* Object Detection */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Environment</h4>
          <div className="text-xs">
            <div className="flex justify-between">
              <span>Prohibited Items:</span>
              <span className={proctoringData.objectDetection.prohibitedItems.length > 0 ? 'text-red-600' : 'text-green-600'}>
                {proctoringData.objectDetection.prohibitedItems.length}
              </span>
            </div>
            {proctoringData.objectDetection.prohibitedItems.length > 0 && (
              <div className="mt-1 text-red-600">
                {proctoringData.objectDetection.prohibitedItems.join(', ')}
              </div>
            )}
          </div>
        </div>

        {/* Multi-Person Detection */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Person Detection</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>People Count:</span>
              <span className={proctoringData.multiPerson.personCount > 1 ? 'text-red-600' : 'text-green-600'}>
                {proctoringData.multiPerson.personCount}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Violations:</span>
              <span className={getScoreColor(proctoringData.multiPerson.violations, true)}>
                {proctoringData.multiPerson.violations}
              </span>
            </div>
          </div>
        </div>

        {/* Voice Analysis */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Audio Analysis</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Background Noise:</span>
              <span className={getScoreColor(proctoringData.voiceAnalysis.backgroundNoise, true)}>
                {proctoringData.voiceAnalysis.backgroundNoise}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>Voice Changes:</span>
              <span className={getScoreColor(proctoringData.voiceAnalysis.voicePatternChanges, true)}>
                {proctoringData.voiceAnalysis.voicePatternChanges}
              </span>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">System Status</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Fullscreen:</span>
              <span className={proctoringData.fullScreen.status === 'on' ? 'text-green-600' : 'text-red-600'}>
                {proctoringData.fullScreen.status === 'on' ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Tab Focus:</span>
              <span className={proctoringData.tabFocus.status ? 'text-green-600' : 'text-red-600'}>
                {proctoringData.tabFocus.status ? 'Active' : 'Lost'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Critical Alerts */}
      <AnimatePresence>
        {overallRiskLevel === 'CRITICAL' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t bg-red-50 p-3"
          >
            <div className="flex items-center gap-2 text-red-700">
              <Icon name="AlertTriangle" className="h-4 w-4" />
              <span className="text-sm font-medium">Critical Violation Detected</span>
            </div>
            <p className="mt-1 text-xs text-red-600">
              Multiple proctoring violations detected. Interview may be flagged for review.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
