'use client';

import { useState } from 'react';
import { X, Calendar, Users, Briefcase, Clock, Star, Save, RotateCcw } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { Button } from '@camped-ui/button';
import { Input } from '@camped-ui/input';
import { Label } from '@camped-ui/label';
import { Badge } from '@camped-ui/badge';
import { Checkbox } from '@camped-ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@camped-ui/select';
import { Separator } from '@camped-ui/separator';

interface FilterState {
  dateRange: { start: Date | null; end: Date | null };
  status: string[];
  interviewType: string[];
  interviewer: string[];
  role: string[];
  searchQuery: string;
  priority: string[];
  scoreRange: { min: number | null; max: number | null };
  meetingType: string[];
  tags: string[];
}

interface AdvancedFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: Partial<FilterState>) => void;
  onClose: () => void;
}

interface SavedFilter {
  id: string;
  name: string;
  filters: FilterState;
  isDefault?: boolean;
}

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  onClose
}) => {
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([
    {
      id: '1',
      name: 'Today\'s Interviews',
      filters: {
        ...filters,
        dateRange: { start: new Date(), end: new Date() },
        status: ['scheduled', 'in-progress']
      },
      isDefault: true
    },
    {
      id: '2',
      name: 'Pending Feedback',
      filters: {
        ...filters,
        status: ['completed'],
        scoreRange: { min: null, max: null }
      }
    },
    {
      id: '3',
      name: 'High Priority',
      filters: {
        ...filters,
        priority: ['high']
      }
    }
  ]);

  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newFilterName, setNewFilterName] = useState('');

  const statusOptions = [
    { value: 'scheduled', label: 'Scheduled', color: 'bg-blue-100 text-blue-800' },
    { value: 'in-progress', label: 'In Progress', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'completed', label: 'Completed', color: 'bg-green-100 text-green-800' },
    { value: 'cancelled', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
    { value: 'no-show', label: 'No Show', color: 'bg-gray-100 text-gray-800' }
  ];

  const interviewTypeOptions = [
    { value: 'technical', label: 'Technical' },
    { value: 'behavioral', label: 'Behavioral' },
    { value: 'system-design', label: 'System Design' },
    { value: 'cultural-fit', label: 'Cultural Fit' },
    { value: 'portfolio-review', label: 'Portfolio Review' },
    { value: 'leadership', label: 'Leadership' }
  ];

  const meetingTypeOptions = [
    { value: 'video', label: 'Video Call' },
    { value: 'phone', label: 'Phone Call' },
    { value: 'in-person', label: 'In Person' }
  ];

  const priorityOptions = [
    { value: 'high', label: 'High', color: 'bg-red-100 text-red-800' },
    { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'low', label: 'Low', color: 'bg-green-100 text-green-800' }
  ];

  const roleOptions = [
    'Software Engineer',
    'Frontend Developer',
    'Backend Developer',
    'Full Stack Developer',
    'Data Scientist',
    'Product Manager',
    'Designer',
    'DevOps Engineer',
    'QA Engineer',
    'Business Analyst'
  ];

  const interviewerOptions = [
    'Mohammed Mubarak',
    'Lokesh K',
    'Balashri Rawat',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  const handleMultiSelectChange = (field: keyof FilterState, value: string, checked: boolean) => {
    const currentValues = filters[field] as string[];
    const newValues = checked 
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    
    onFiltersChange({ [field]: newValues });
  };

  const handleDateRangeChange = (range: { start: Date | null; end: Date | null }) => {
    onFiltersChange({ dateRange: range });
  };

  const handleScoreRangeChange = (type: 'min' | 'max', value: string) => {
    const numValue = value === '' ? null : parseFloat(value);
    onFiltersChange({
      scoreRange: {
        ...filters.scoreRange,
        [type]: numValue
      }
    });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      dateRange: { start: null, end: null },
      status: [],
      interviewType: [],
      interviewer: [],
      role: [],
      searchQuery: '',
      priority: [],
      scoreRange: { min: null, max: null },
      meetingType: [],
      tags: []
    });
  };

  const handleSaveFilter = () => {
    if (!newFilterName.trim()) return;
    
    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: newFilterName,
      filters: { ...filters }
    };
    
    setSavedFilters(prev => [...prev, newFilter]);
    setNewFilterName('');
    setShowSaveDialog(false);
  };

  const handleLoadFilter = (savedFilter: SavedFilter) => {
    onFiltersChange(savedFilter.filters);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.status.length > 0) count++;
    if (filters.interviewType.length > 0) count++;
    if (filters.interviewer.length > 0) count++;
    if (filters.role.length > 0) count++;
    if (filters.priority.length > 0) count++;
    if (filters.scoreRange.min !== null || filters.scoreRange.max !== null) count++;
    if (filters.meetingType.length > 0) count++;
    if (filters.tags.length > 0) count++;
    return count;
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="flex items-center gap-2">
          <span>Advanced Filters</span>
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary">{getActiveFilterCount()} active</Badge>
          )}
        </CardTitle>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleClearFilters}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Clear All
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowSaveDialog(true)}>
            <Save className="h-4 w-4 mr-1" />
            Save Filter
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Saved Filters */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Quick Filters</Label>
          <div className="flex flex-wrap gap-2">
            {savedFilters.map(savedFilter => (
              <Button
                key={savedFilter.id}
                variant="outline"
                size="sm"
                onClick={() => handleLoadFilter(savedFilter)}
                className="text-xs"
              >
                {savedFilter.name}
                {savedFilter.isDefault && <Star className="h-3 w-3 ml-1 text-yellow-500" />}
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Date Range */}
        <div>
          <Label className="text-sm font-medium mb-2 block flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date Range
          </Label>
          <div className="flex gap-2">
            <Input
              type="date"
              placeholder="Start date"
              value={filters.dateRange.start ? filters.dateRange.start.toISOString().split('T')[0] : ''}
              onChange={(e) => handleDateRangeChange({
                start: e.target.value ? new Date(e.target.value) : null,
                end: filters.dateRange.end
              })}
            />
            <Input
              type="date"
              placeholder="End date"
              value={filters.dateRange.end ? filters.dateRange.end.toISOString().split('T')[0] : ''}
              onChange={(e) => handleDateRangeChange({
                start: filters.dateRange.start,
                end: e.target.value ? new Date(e.target.value) : null
              })}
            />
          </div>
        </div>

        {/* Status */}
        <div>
          <Label className="text-sm font-medium mb-2 block flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Interview Status
          </Label>
          <div className="flex flex-wrap gap-2">
            {statusOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${option.value}`}
                  checked={filters.status.includes(option.value)}
                  onCheckedChange={(checked) => 
                    handleMultiSelectChange('status', option.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`status-${option.value}`}
                  className={`text-xs px-2 py-1 rounded ${option.color} cursor-pointer`}
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Priority */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Priority</Label>
          <div className="flex flex-wrap gap-2">
            {priorityOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${option.value}`}
                  checked={filters.priority.includes(option.value)}
                  onCheckedChange={(checked) => 
                    handleMultiSelectChange('priority', option.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`priority-${option.value}`}
                  className={`text-xs px-2 py-1 rounded ${option.color} cursor-pointer`}
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Interview Type */}
        <div>
          <Label className="text-sm font-medium mb-2 block flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Interview Type
          </Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {interviewTypeOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${option.value}`}
                  checked={filters.interviewType.includes(option.value)}
                  onCheckedChange={(checked) => 
                    handleMultiSelectChange('interviewType', option.value, checked as boolean)
                  }
                />
                <Label htmlFor={`type-${option.value}`} className="text-sm cursor-pointer">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Meeting Type */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Meeting Type</Label>
          <div className="flex gap-4">
            {meetingTypeOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`meeting-${option.value}`}
                  checked={filters.meetingType.includes(option.value)}
                  onCheckedChange={(checked) => 
                    handleMultiSelectChange('meetingType', option.value, checked as boolean)
                  }
                />
                <Label htmlFor={`meeting-${option.value}`} className="text-sm cursor-pointer">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Role */}
        <div>
          <Label className="text-sm font-medium mb-2 block flex items-center gap-2">
            <Users className="h-4 w-4" />
            Role
          </Label>
          <Select
            value={filters.role[0] || ''}
            onValueChange={(value) => onFiltersChange({ role: value ? [value] : [] })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              {roleOptions.map(role => (
                <SelectItem key={role} value={role}>{role}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Interviewer */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Interviewer</Label>
          <Select
            value={filters.interviewer[0] || ''}
            onValueChange={(value) => onFiltersChange({ interviewer: value ? [value] : [] })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select interviewer" />
            </SelectTrigger>
            <SelectContent>
              {interviewerOptions.map(interviewer => (
                <SelectItem key={interviewer} value={interviewer}>{interviewer}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Score Range */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Score Range</Label>
          <div className="flex items-center gap-2">
            <Input
              type="number"
              placeholder="Min"
              min="0"
              max="10"
              step="0.1"
              value={filters.scoreRange.min || ''}
              onChange={(e) => handleScoreRangeChange('min', e.target.value)}
              className="w-20"
            />
            <span className="text-muted-foreground">to</span>
            <Input
              type="number"
              placeholder="Max"
              min="0"
              max="10"
              step="0.1"
              value={filters.scoreRange.max || ''}
              onChange={(e) => handleScoreRangeChange('max', e.target.value)}
              className="w-20"
            />
          </div>
        </div>

        {/* Save Filter Dialog */}
        {showSaveDialog && (
          <div className="border rounded-lg p-4 bg-muted/50">
            <Label className="text-sm font-medium mb-2 block">Save Current Filter</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Filter name"
                value={newFilterName}
                onChange={(e) => setNewFilterName(e.target.value)}
                className="flex-1"
              />
              <Button size="sm" onClick={handleSaveFilter}>Save</Button>
              <Button size="sm" variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
