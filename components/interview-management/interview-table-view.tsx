'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { 
  MoreHorizontal, 
  Eye, 
  Calendar, 
  Phone, 
  Video, 
  MapPin, 
  Star,
  Clock,
  User,
  Mail,
  Edit,
  Trash2
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { Button } from '@camped-ui/button';
import { Badge } from '@camped-ui/badge';
import { Checkbox } from '@camped-ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@camped-ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@camped-ui/dropdown-menu';
import { Too<PERSON><PERSON>, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@camped-ui/tooltip';
import { getInterviewDashboardData } from '@/services/apicall';

interface Interview {
  id: string;
  candidateName: string;
  candidateEmail: string;
  candidateAvatar?: string;
  role: string;
  level: string;
  interviewName: string;
  scheduleTime: string;
  meetingType: 'video' | 'phone' | 'in-person';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  interviewer: string;
  score?: number;
  feedback?: string;
  eventId?: string;
  careerPracticeId?: string;
  priority: 'high' | 'medium' | 'low';
  tags: string[];
  createdAt: string;
}

interface InterviewTableViewProps {
  organizationId: string;
  userId: string;
  userRole: string;
  filters: any;
  selectedInterviews: string[];
  onSelectionChange: (ids: string[]) => void;
}

export const InterviewTableView: React.FC<InterviewTableViewProps> = ({
  organizationId,
  userId,
  userRole,
  filters,
  selectedInterviews,
  onSelectionChange
}) => {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Interview;
    direction: 'asc' | 'desc';
  } | null>(null);

  useEffect(() => {
    fetchInterviews();
  }, [organizationId, filters]);

  const fetchInterviews = async () => {
    setIsLoading(true);
    try {
      const data = await getInterviewDashboardData(organizationId, userId, userRole, filters);
      setInterviews(data.interviews || []);
    } catch (error) {
      console.error('Failed to fetch interviews:', error);
      setInterviews([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSort = (key: keyof Interview) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedInterviews = [...interviews].sort((a, b) => {
    if (!sortConfig) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    // Handle undefined values
    if (aValue === undefined && bValue === undefined) return 0;
    if (aValue === undefined) return 1;
    if (bValue === undefined) return -1;

    if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(interviews.map(interview => interview.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectInterview = (interviewId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedInterviews, interviewId]);
    } else {
      onSelectionChange(selectedInterviews.filter(id => id !== interviewId));
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: { color: 'bg-blue-100 text-blue-800', label: 'Scheduled' },
      'in-progress': { color: 'bg-yellow-100 text-yellow-800', label: 'In Progress' },
      completed: { color: 'bg-green-100 text-green-800', label: 'Completed' },
      cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
      'no-show': { color: 'bg-gray-100 text-gray-800', label: 'No Show' }
    };
    
    const config = statusConfig[status] || statusConfig.scheduled;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      high: { color: 'bg-red-100 text-red-800 border-red-200', label: 'High' },
      medium: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Medium' },
      low: { color: 'bg-green-100 text-green-800 border-green-200', label: 'Low' }
    };
    
    const config = priorityConfig[priority] || priorityConfig.medium;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getMeetingIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4 text-blue-600" />;
      case 'phone': return <Phone className="h-4 w-4 text-green-600" />;
      case 'in-person': return <MapPin className="h-4 w-4 text-purple-600" />;
      default: return <Video className="h-4 w-4 text-blue-600" />;
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: format(date, 'MMM dd, yyyy'),
      time: format(date, 'HH:mm')
    };
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading interviews...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Interview List</span>
            <Badge variant="secondary">{interviews.length} interviews</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedInterviews.length === interviews.length && interviews.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('candidateName')}
                  >
                    Candidate
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('role')}
                  >
                    Role & Interview
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('scheduleTime')}
                  >
                    Schedule
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Interviewer</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedInterviews.map((interview) => {
                  const { date, time } = formatDateTime(interview.scheduleTime);
                  const isSelected = selectedInterviews.includes(interview.id);
                  
                  return (
                    <TableRow 
                      key={interview.id}
                      className={`${isSelected ? 'bg-blue-50' : ''} hover:bg-muted/50`}
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => 
                            handleSelectInterview(interview.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={interview.candidateAvatar} />
                            <AvatarFallback>
                              {interview.candidateName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{interview.candidateName}</p>
                            <p className="text-sm text-muted-foreground">{interview.candidateEmail}</p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <p className="font-medium">{interview.role}</p>
                          <p className="text-sm text-muted-foreground">{interview.interviewName}</p>
                          <div className="flex gap-1 mt-1">
                            {interview.tags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div>
                            <p className="font-medium">{date}</p>
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {time}
                            </p>
                          </div>
                          <Tooltip>
                            <TooltipTrigger>
                              {getMeetingIcon(interview.meetingType)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {interview.meetingType} interview
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(interview.status)}
                      </TableCell>
                      
                      <TableCell>
                        {getPriorityBadge(interview.priority)}
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{interview.interviewer}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {interview.score ? (
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">{interview.score}/10</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Interview
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Calendar className="mr-2 h-4 w-4" />
                              Reschedule
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              Send Email
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Cancel Interview
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};
