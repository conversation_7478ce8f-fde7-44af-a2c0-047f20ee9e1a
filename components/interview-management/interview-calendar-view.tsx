'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Calendar, Clock, User, Video } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { But<PERSON> } from '@camped-ui/button';
import { Badge } from '@camped-ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { getInterviewDashboardData } from '@/services/apicall';

interface Interview {
  id: string;
  candidateName: string;
  candidateEmail: string;
  role: string;
  interviewName: string;
  scheduleTime: string;
  meetingType: string;
  status: string;
  interviewer: string;
  priority: string;
}

interface InterviewCalendarViewProps {
  organizationId: string;
  userId: string;
  userRole: string;
  filters: any;
  onInterviewSelect: (id: string) => void;
}

export const InterviewCalendarView: React.FC<InterviewCalendarViewProps> = ({
  organizationId,
  userId,
  userRole,
  filters,
  onInterviewSelect
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('week');

  useEffect(() => {
    fetchInterviews();
  }, [organizationId, filters, currentDate]);

  const fetchInterviews = async () => {
    setIsLoading(true);
    try {
      const data = await getInterviewDashboardData(organizationId, userId, userRole, filters);
      setInterviews(data.interviews || []);
    } catch (error) {
      console.error('Failed to fetch interviews:', error);
      setInterviews([]);
    } finally {
      setIsLoading(false);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    
    if (viewMode === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    } else if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    }
    
    setCurrentDate(newDate);
  };

  const getWeekDays = (): Date[] => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day;
    startOfWeek.setDate(diff);

    const days: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      days.push(date);
    }
    return days;
  };

  const getInterviewsForDate = (date: Date) => {
    return interviews.filter(interview => {
      const interviewDate = new Date(interview.scheduleTime);
      return interviewDate.toDateString() === date.toDateString();
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading calendar...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-100 rounded animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Interview Calendar
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <div className="flex rounded-lg border">
              {(['day', 'week', 'month'] as const).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className="capitalize"
                >
                  {mode}
                </Button>
              ))}
            </div>
            
            <div className="flex items-center gap-1">
              <Button variant="outline" size="sm" onClick={() => navigateDate('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={() => navigateDate('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        
        <div className="text-lg font-medium">
          {currentDate.toLocaleDateString('en-US', { 
            month: 'long', 
            year: 'numeric',
            ...(viewMode === 'day' && { day: 'numeric' })
          })}
        </div>
      </CardHeader>
      
      <CardContent>
        {viewMode === 'week' && (
          <div className="grid grid-cols-7 gap-4">
            {getWeekDays().map((day, index) => {
              const dayInterviews = getInterviewsForDate(day);
              const isToday = day.toDateString() === new Date().toDateString();
              
              return (
                <div key={index} className="min-h-[300px]">
                  <div className={`text-center p-2 rounded-t-lg ${
                    isToday ? 'bg-blue-100 text-blue-800 font-semibold' : 'bg-gray-50'
                  }`}>
                    <div className="text-sm">
                      {day.toLocaleDateString('en-US', { weekday: 'short' })}
                    </div>
                    <div className="text-lg">
                      {day.getDate()}
                    </div>
                  </div>
                  
                  <div className="space-y-2 p-2 border border-t-0 rounded-b-lg min-h-[250px]">
                    {dayInterviews.map((interview) => (
                      <div
                        key={interview.id}
                        className={`p-2 rounded border-l-4 cursor-pointer hover:shadow-md transition-shadow ${getPriorityColor(interview.priority)}`}
                        onClick={() => onInterviewSelect(interview.id)}
                      >
                        <div className="text-xs font-medium text-gray-600 mb-1">
                          {formatTime(interview.scheduleTime)}
                        </div>
                        
                        <div className="text-sm font-medium truncate">
                          {interview.candidateName}
                        </div>
                        
                        <div className="text-xs text-gray-600 truncate">
                          {interview.role}
                        </div>
                        
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center gap-1">
                            {interview.meetingType === 'video' && <Video className="h-3 w-3" />}
                            <User className="h-3 w-3" />
                          </div>
                          
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              interview.priority === 'high' ? 'border-red-300 text-red-700' :
                              interview.priority === 'medium' ? 'border-yellow-300 text-yellow-700' :
                              'border-green-300 text-green-700'
                            }`}
                          >
                            {interview.priority}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    
                    {dayInterviews.length === 0 && (
                      <div className="text-center text-gray-400 text-sm py-8">
                        No interviews
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
        {viewMode === 'day' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {Array.from({ length: 24 }, (_, hour) => {
                const hourInterviews = interviews.filter(interview => {
                  const interviewDate = new Date(interview.scheduleTime);
                  return interviewDate.toDateString() === currentDate.toDateString() &&
                         interviewDate.getHours() === hour;
                });
                
                return (
                  <div key={hour} className="flex border-b pb-2">
                    <div className="w-20 text-sm text-gray-500 py-2">
                      {hour.toString().padStart(2, '0')}:00
                    </div>
                    
                    <div className="flex-1 min-h-[60px]">
                      {hourInterviews.map((interview) => (
                        <div
                          key={interview.id}
                          className={`p-3 rounded border-l-4 cursor-pointer hover:shadow-md transition-shadow mb-2 ${getPriorityColor(interview.priority)}`}
                          onClick={() => onInterviewSelect(interview.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium">{interview.candidateName}</div>
                              <div className="text-sm text-gray-600">{interview.role}</div>
                              <div className="text-sm text-gray-500">{interview.interviewName}</div>
                            </div>
                            
                            <div className="text-right">
                              <div className="text-sm font-medium">
                                {formatTime(interview.scheduleTime)}
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <Avatar className="h-6 w-6">
                                  <AvatarFallback className="text-xs">
                                    {interview.interviewer.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <Badge variant="outline" className="text-xs">
                                  {interview.priority}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        {viewMode === 'month' && (
          <div className="text-center py-8 text-gray-500">
            Month view coming soon...
          </div>
        )}
      </CardContent>
    </Card>
  );
};
