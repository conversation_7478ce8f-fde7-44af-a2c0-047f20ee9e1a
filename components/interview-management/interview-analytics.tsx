'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar, 
  Clock, 
  Star,
  Target,
  Award,
  BarChart3,
  Pie<PERSON><PERSON>
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';
import { Progress } from '@camped-ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@camped-ui/tabs';
import { getInterviewAnalytics } from '@/services/apicall';

interface AnalyticsData {
  overview: {
    totalInterviews: number;
    completedInterviews: number;
    averageScore: number;
    completionRate: number;
    noShowRate: number;
    averageDuration: number;
  };
  trends: {
    interviewsThisMonth: number;
    interviewsLastMonth: number;
    scoresThisMonth: number;
    scoresLastMonth: number;
    completionThisMonth: number;
    completionLastMonth: number;
  };
  byRole: Array<{
    role: string;
    count: number;
    averageScore: number;
    completionRate: number;
  }>;
  byInterviewer: Array<{
    interviewer: string;
    count: number;
    averageScore: number;
    completionRate: number;
  }>;
  byStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  timeDistribution: Array<{
    hour: number;
    count: number;
  }>;
}

interface InterviewAnalyticsProps {
  organizationId: string;
  userId: string;
  userRole: string;
  filters: any;
}

export const InterviewAnalytics: React.FC<InterviewAnalyticsProps> = ({
  organizationId,
  userId,
  userRole,
  filters
}) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalytics();
  }, [organizationId, filters]);

  const fetchAnalytics = async () => {
    setIsLoading(true);
    try {
      const data = await getInterviewAnalytics(organizationId, userId, userRole, filters);
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      // Set default analytics on error
      setAnalytics({
        overview: {
          totalInterviews: 0,
          completedInterviews: 0,
          averageScore: 0,
          completionRate: 0,
          noShowRate: 0,
          averageDuration: 0
        },
        trends: {
          interviewsThisMonth: 0,
          interviewsLastMonth: 0,
          scoresThisMonth: 0,
          scoresLastMonth: 0,
          completionThisMonth: 0,
          completionLastMonth: 0
        },
        byRole: [],
        byInterviewer: [],
        byStatus: [],
        timeDistribution: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const TrendIndicator = ({ current, previous, suffix = '', isPercentage = false }: any) => {
    const trend = calculateTrend(current, previous);
    const isPositive = trend > 0;
    
    return (
      <div className="flex items-center gap-2">
        <span className="text-2xl font-bold">
          {isPercentage ? `${current}%` : current}{suffix}
        </span>
        <div className={`flex items-center gap-1 text-sm ${
          isPositive ? 'text-green-600' : 'text-red-600'
        }`}>
          {isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
          {Math.abs(trend).toFixed(1)}%
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <TrendIndicator 
                  current={analytics.trends.interviewsThisMonth}
                  previous={analytics.trends.interviewsLastMonth}
                />
                <p className="text-xs text-muted-foreground">vs last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <TrendIndicator 
                  current={analytics.trends.scoresThisMonth}
                  previous={analytics.trends.scoresLastMonth}
                  suffix="/10"
                />
                <p className="text-xs text-muted-foreground">vs last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <TrendIndicator 
                  current={analytics.trends.completionThisMonth}
                  previous={analytics.trends.completionLastMonth}
                  isPercentage={true}
                />
                <p className="text-xs text-muted-foreground">vs last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.overview.averageDuration}min</div>
                <p className="text-xs text-muted-foreground">per interview</p>
              </CardContent>
            </Card>
          </div>

          {/* Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Interview Status Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.byStatus.map((status) => (
                  <div key={status.status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant="outline"
                        className={
                          status.status === 'Completed' ? 'border-green-200 text-green-800' :
                          status.status === 'Scheduled' ? 'border-blue-200 text-blue-800' :
                          status.status === 'No Show' ? 'border-red-200 text-red-800' :
                          'border-gray-200 text-gray-800'
                        }
                      >
                        {status.status}
                      </Badge>
                      <span className="text-sm">{status.count} interviews</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress value={status.percentage} className="w-20" />
                      <span className="text-sm font-medium">{status.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Performance by Role */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Performance by Role
              </CardTitle>
              <CardDescription>Interview metrics broken down by job role</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.byRole.map((role) => (
                  <div key={role.role} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{role.role}</span>
                      <Badge variant="secondary">{role.count} interviews</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Avg Score:</span>
                        <span className="font-medium">{role.averageScore}/10</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Completion:</span>
                        <span className="font-medium">{role.completionRate}%</span>
                      </div>
                    </div>
                    <Progress value={role.completionRate} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance by Interviewer */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Performance by Interviewer
              </CardTitle>
              <CardDescription>Interview metrics by interviewer</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.byInterviewer.map((interviewer) => (
                  <div key={interviewer.interviewer} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{interviewer.interviewer}</span>
                      <Badge variant="secondary">{interviewer.count} interviews</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Avg Score:</span>
                        <span className="font-medium">{interviewer.averageScore}/10</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Completion:</span>
                        <span className="font-medium">{interviewer.completionRate}%</span>
                      </div>
                    </div>
                    <Progress value={interviewer.completionRate} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Interview Time Distribution</CardTitle>
              <CardDescription>Most popular interview times</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.timeDistribution.map((time) => (
                  <div key={time.hour} className="flex items-center gap-4">
                    <span className="w-16 text-sm font-medium">
                      {time.hour}:00
                    </span>
                    <Progress value={(time.count / 25) * 100} className="flex-1" />
                    <span className="w-12 text-sm text-muted-foreground">
                      {time.count}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                    <span className="font-medium">Best Interviewer</span>
                    <span className="text-sm">Mohammed Mubarak (8.3/10)</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                    <span className="font-medium">Highest Completion</span>
                    <span className="text-sm">Software Engineer (94.4%)</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                    <span className="font-medium">Most Active Role</span>
                    <span className="text-sm">Software Engineer (45)</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                    <p className="font-medium">Schedule Optimization</p>
                    <p className="text-muted-foreground">
                      Peak interview times are 2-3 PM. Consider scheduling more interviews during this slot.
                    </p>
                  </div>
                  <div className="p-3 bg-green-50 rounded border-l-4 border-green-400">
                    <p className="font-medium">No-Show Reduction</p>
                    <p className="text-muted-foreground">
                      Send reminder emails 24 hours before interviews to reduce no-show rate.
                    </p>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                    <p className="font-medium">Interviewer Training</p>
                    <p className="text-muted-foreground">
                      Consider additional training for interviewers with scores below 7.5.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
