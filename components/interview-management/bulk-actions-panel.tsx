'use client';

import { useState } from 'react';
import { 
  Calendar, 
  Mail, 
  X, 
  Clock, 
  UserX, 
  MessageSquare, 
  Download,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

import { Card, CardContent } from '@camped-ui/card';
import { Button } from '@camped-ui/button';
import { Badge } from '@camped-ui/badge';
import { Separator } from '@camped-ui/separator';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@camped-ui/dialog';
import { Input } from '@camped-ui/input';
import { Label } from '@camped-ui/label';
import { Textarea } from '@camped-ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@camped-ui/select';

interface BulkActionsPanelProps {
  selectedCount: number;
  selectedInterviews: string[];
  onAction: (action: string, interviewIds: string[], data?: any) => void;
  onClearSelection: () => void;
}

interface BulkActionDialog {
  type: 'reschedule' | 'cancel' | 'email' | 'status' | null;
  isOpen: boolean;
}

export const BulkActionsPanel: React.FC<BulkActionsPanelProps> = ({
  selectedCount,
  selectedInterviews,
  onAction,
  onClearSelection
}) => {
  const [dialog, setDialog] = useState<BulkActionDialog>({ type: null, isOpen: false });
  const [formData, setFormData] = useState<any>({});
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAction = async (action: string, data?: any) => {
    setIsProcessing(true);
    try {
      await onAction(action, selectedInterviews, data);
      setDialog({ type: null, isOpen: false });
      setFormData({});
      onClearSelection();
    } catch (error) {
      console.error('Bulk action failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const openDialog = (type: BulkActionDialog['type']) => {
    setDialog({ type, isOpen: true });
    setFormData({});
  };

  const closeDialog = () => {
    setDialog({ type: null, isOpen: false });
    setFormData({});
  };

  const bulkActions = [
    {
      id: 'reschedule',
      label: 'Reschedule',
      icon: Calendar,
      variant: 'outline' as const,
      description: 'Change interview dates and times'
    },
    {
      id: 'cancel',
      label: 'Cancel',
      icon: X,
      variant: 'destructive' as const,
      description: 'Cancel selected interviews'
    },
    {
      id: 'email',
      label: 'Send Email',
      icon: Mail,
      variant: 'outline' as const,
      description: 'Send notifications to candidates'
    },
    {
      id: 'status',
      label: 'Update Status',
      icon: CheckCircle,
      variant: 'outline' as const,
      description: 'Change interview status'
    },
    {
      id: 'export',
      label: 'Export',
      icon: Download,
      variant: 'outline' as const,
      description: 'Download interview data'
    }
  ];

  return (
    <>
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {selectedCount} selected
              </Badge>
              <span className="text-sm text-muted-foreground">
                Bulk actions available
              </span>
            </div>

            <div className="flex items-center gap-2">
              {bulkActions.map((action, index) => (
                <div key={action.id} className="flex items-center">
                  {index > 0 && <Separator orientation="vertical" className="h-6 mx-1" />}
                  <Button
                    size="sm"
                    variant={action.variant}
                    onClick={() => {
                      if (action.id === 'export') {
                        handleAction('export');
                      } else {
                        openDialog(action.id as any);
                      }
                    }}
                    disabled={isProcessing}
                    className="flex items-center gap-2"
                  >
                    <action.icon className="h-4 w-4" />
                    {action.label}
                  </Button>
                </div>
              ))}
              
              <Separator orientation="vertical" className="h-6 mx-1" />
              
              <Button
                size="sm"
                variant="ghost"
                onClick={onClearSelection}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear Selection
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reschedule Dialog */}
      <Dialog open={dialog.type === 'reschedule' && dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reschedule Interviews</DialogTitle>
            <DialogDescription>
              Reschedule {selectedCount} selected interview{selectedCount > 1 ? 's' : ''}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-date">New Date</Label>
              <Input
                id="new-date"
                type="date"
                value={formData.newDate || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, newDate: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="new-time">New Time</Label>
              <Input
                id="new-time"
                type="time"
                value={formData.newTime || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, newTime: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="reschedule-reason">Reason (Optional)</Label>
              <Textarea
                id="reschedule-reason"
                placeholder="Reason for rescheduling..."
                value={formData.reason || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>Cancel</Button>
            <Button 
              onClick={() => handleAction('reschedule', formData)}
              disabled={!formData.newDate || !formData.newTime || isProcessing}
            >
              {isProcessing ? 'Rescheduling...' : 'Reschedule'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Dialog */}
      <Dialog open={dialog.type === 'cancel' && dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Cancel Interviews
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel {selectedCount} selected interview{selectedCount > 1 ? 's' : ''}? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="cancel-reason">Cancellation Reason</Label>
              <Select
                value={formData.cancelReason || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, cancelReason: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="candidate-request">Candidate Request</SelectItem>
                  <SelectItem value="interviewer-unavailable">Interviewer Unavailable</SelectItem>
                  <SelectItem value="technical-issues">Technical Issues</SelectItem>
                  <SelectItem value="position-filled">Position Filled</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="cancel-notes">Additional Notes (Optional)</Label>
              <Textarea
                id="cancel-notes"
                placeholder="Additional details..."
                value={formData.notes || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="notify-candidates"
                checked={formData.notifyCandidates || false}
                onChange={(e) => setFormData(prev => ({ ...prev, notifyCandidates: e.target.checked }))}
              />
              <Label htmlFor="notify-candidates">Notify candidates via email</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>Keep Interviews</Button>
            <Button 
              variant="destructive"
              onClick={() => handleAction('cancel', formData)}
              disabled={!formData.cancelReason || isProcessing}
            >
              {isProcessing ? 'Cancelling...' : 'Cancel Interviews'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Dialog */}
      <Dialog open={dialog.type === 'email' && dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Send Email to Candidates</DialogTitle>
            <DialogDescription>
              Send email to {selectedCount} candidate{selectedCount > 1 ? 's' : ''}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="email-template">Email Template</Label>
              <Select
                value={formData.template || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, template: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select template" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reminder">Interview Reminder</SelectItem>
                  <SelectItem value="confirmation">Interview Confirmation</SelectItem>
                  <SelectItem value="reschedule">Reschedule Notification</SelectItem>
                  <SelectItem value="preparation">Interview Preparation</SelectItem>
                  <SelectItem value="custom">Custom Message</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="email-subject">Subject</Label>
              <Input
                id="email-subject"
                placeholder="Email subject..."
                value={formData.subject || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="email-message">Message</Label>
              <Textarea
                id="email-message"
                placeholder="Email message..."
                rows={6}
                value={formData.message || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>Cancel</Button>
            <Button 
              onClick={() => handleAction('email', formData)}
              disabled={!formData.subject || !formData.message || isProcessing}
            >
              {isProcessing ? 'Sending...' : 'Send Email'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog open={dialog.type === 'status' && dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Interview Status</DialogTitle>
            <DialogDescription>
              Update status for {selectedCount} selected interview{selectedCount > 1 ? 's' : ''}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-status">New Status</Label>
              <Select
                value={formData.newStatus || ''}
                onValueChange={(value) => setFormData(prev => ({ ...prev, newStatus: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="status-notes">Notes (Optional)</Label>
              <Textarea
                id="status-notes"
                placeholder="Reason for status change..."
                value={formData.statusNotes || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, statusNotes: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog}>Cancel</Button>
            <Button 
              onClick={() => handleAction('status', formData)}
              disabled={!formData.newStatus || isProcessing}
            >
              {isProcessing ? 'Updating...' : 'Update Status'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
