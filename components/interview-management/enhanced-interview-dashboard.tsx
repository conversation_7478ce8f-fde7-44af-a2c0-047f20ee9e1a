'use client';

import { useState, useEffect } from 'react';
import { Calendar, Filter, Users, Clock, TrendingUp, Search, Plus, Download } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { But<PERSON> } from '@camped-ui/button';
import { Input } from '@camped-ui/input';
import { Badge } from '@camped-ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@camped-ui/tabs';

import { InterviewPipelineView } from './interview-pipeline-view';
import { InterviewCalendarView } from './interview-calendar-view';
import { InterviewTableView } from './interview-table-view';
import { InterviewAnalytics } from './interview-analytics';
import { BulkActionsPanel } from './bulk-actions-panel';
import { AdvancedFilters } from './advanced-filters';
import { getInterviewDashboardData, performBulkInterviewAction } from '@/services/apicall';

interface EnhancedInterviewDashboardProps {
  organizationId: string;
  userRole: string;
  userId: string;
}

interface DashboardMetrics {
  totalScheduled: number;
  todayInterviews: number;
  completedThisWeek: number;
  averageScore: number;
  completionRate: number;
  pendingFeedback: number;
}

interface FilterState {
  dateRange: { start: Date | null; end: Date | null };
  status: string[];
  interviewType: string[];
  interviewer: string[];
  role: string[];
  searchQuery: string;
}

export const EnhancedInterviewDashboard: React.FC<EnhancedInterviewDashboardProps> = ({
  organizationId,
  userRole,
  userId
}) => {
  const [activeView, setActiveView] = useState<'pipeline' | 'calendar' | 'table'>('calendar');
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [selectedInterviews, setSelectedInterviews] = useState<string[]>([]);
  const [filters, setFilters] = useState<FilterState>({
    dateRange: { start: null, end: null },
    status: [],
    interviewType: [],
    interviewer: [],
    role: [],
    searchQuery: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardMetrics();
  }, [organizationId, filters]);

  const fetchDashboardMetrics = async () => {
    setIsLoading(true);
    try {
      const data = await getInterviewDashboardData(organizationId, userId, userRole, filters);
      setMetrics(data.metrics);
    } catch (error) {
      console.error('Failed to fetch dashboard metrics:', error);
      // Fallback to default metrics on error
      setMetrics({
        totalScheduled: 0,
        todayInterviews: 0,
        completedThisWeek: 0,
        averageScore: 0,
        completionRate: 0,
        pendingFeedback: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkAction = async (action: string, interviewIds: string[], data?: any) => {
    try {
      const result = await performBulkInterviewAction(action, interviewIds, data, userId, organizationId);
      console.log(`Successfully performed ${action}:`, result);

      // Refresh data after bulk action
      await fetchDashboardMetrics();

      return result;
    } catch (error) {
      console.error(`Failed to perform ${action}:`, error);
      throw error;
    }
  };

  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const MetricCard = ({ title, value, subtitle, icon: Icon, trend }: any) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">
          {subtitle}
          {trend && (
            <span className={`ml-1 ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {trend > 0 ? '↗' : '↘'} {Math.abs(trend)}%
            </span>
          )}
        </p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Interview Management</h1>
          <p className="text-muted-foreground">
            Manage and track all interviews across your organization
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Schedule Interview
          </Button>
        </div>
      </div>

      {/* Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search candidates, roles, or interview names..."
            value={filters.searchQuery}
            onChange={(e) => handleFilterChange({ searchQuery: e.target.value })}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Badge variant="secondary" className="cursor-pointer">
            Today: {metrics?.todayInterviews || 0}
          </Badge>
          <Badge variant="secondary" className="cursor-pointer">
            This Week: {metrics?.completedThisWeek || 0}
          </Badge>
          <Badge variant="secondary" className="cursor-pointer">
            Pending: {metrics?.pendingFeedback || 0}
          </Badge>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <AdvancedFilters
          filters={filters}
          onFiltersChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <MetricCard
          title="Total Scheduled"
          value={metrics?.totalScheduled || 0}
          subtitle="Active interviews"
          icon={Calendar}
          trend={5}
        />
        <MetricCard
          title="Today's Interviews"
          value={metrics?.todayInterviews || 0}
          subtitle="Scheduled for today"
          icon={Clock}
          trend={-2}
        />
        <MetricCard
          title="Completed This Week"
          value={metrics?.completedThisWeek || 0}
          subtitle="Successfully completed"
          icon={Users}
          trend={12}
        />
        <MetricCard
          title="Average Score"
          value={metrics?.averageScore || 0}
          subtitle="Out of 10"
          icon={TrendingUp}
          trend={3}
        />
        <MetricCard
          title="Completion Rate"
          value={`${metrics?.completionRate || 0}%`}
          subtitle="Interview attendance"
          icon={TrendingUp}
          trend={1}
        />
        <MetricCard
          title="Pending Feedback"
          value={metrics?.pendingFeedback || 0}
          subtitle="Awaiting review"
          icon={Clock}
          trend={-8}
        />
      </div>

      {/* Bulk Actions Panel */}
      {selectedInterviews.length > 0 && (
        <BulkActionsPanel
          selectedCount={selectedInterviews.length}
          onAction={handleBulkAction}
          selectedInterviews={selectedInterviews}
          onClearSelection={() => setSelectedInterviews([])}
        />
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeView} onValueChange={(value: any) => setActiveView(value)}>
        <TabsList className="grid w-full grid-cols-3">
           <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="pipeline">Pipeline View</TabsTrigger>
         
          <TabsTrigger value="table">Table View</TabsTrigger>
        
        </TabsList>

        <TabsContent value="pipeline" className="mt-6">
          <InterviewPipelineView
            organizationId={organizationId}
            userId={userId}
            userRole={userRole}
            filters={filters}
            selectedInterviews={selectedInterviews}
            onSelectionChange={setSelectedInterviews}
          />
        </TabsContent>

        <TabsContent value="calendar" className="mt-6">
          <InterviewCalendarView
            organizationId={organizationId}
            userId={userId}
            userRole={userRole}
            filters={filters}
            onInterviewSelect={(id: string) => setSelectedInterviews([id])}
          />
        </TabsContent>

        <TabsContent value="table" className="mt-6">
          <InterviewTableView
            organizationId={organizationId}
            userId={userId}
            userRole={userRole}
            filters={filters}
            selectedInterviews={selectedInterviews}
            onSelectionChange={setSelectedInterviews}
          />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <InterviewAnalytics
            organizationId={organizationId}
            userId={userId}
            userRole={userRole}
            filters={filters}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
