'use client';

import { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Calendar, Clock, User, MapPin, Phone, Video, MessageSquare, Star } from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@camped-ui/tooltip';
import { getInterviewDashboardData, performBulkInterviewAction } from '@/services/apicall';

interface Interview {
  id: string;
  candidateName: string;
  candidateEmail: string;
  candidateAvatar?: string;
  role: string;
  level: string;
  interviewName: string;
  scheduleTime: string;
  meetingType: 'video' | 'phone' | 'in-person';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  interviewer: string;
  score?: number;
  feedback?: string;
  eventId?: string;
  careerPracticeId?: string;
  priority: 'high' | 'medium' | 'low';
  tags: string[];
}

interface PipelineColumn {
  id: string;
  title: string;
  status: string;
  color: string;
  interviews: Interview[];
}

interface InterviewPipelineViewProps {
  organizationId: string;
  userId: string;
  userRole: string;
  filters: any;
  selectedInterviews: string[];
  onSelectionChange: (ids: string[]) => void;
}

export const InterviewPipelineView: React.FC<InterviewPipelineViewProps> = ({
  organizationId,
  userId,
  userRole,
  filters,
  selectedInterviews,
  onSelectionChange
}) => {
  const [columns, setColumns] = useState<PipelineColumn[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchInterviews();
  }, [organizationId, filters]);

  const fetchInterviews = async () => {
    setIsLoading(true);
    try {
      const data = await getInterviewDashboardData(organizationId, userId, userRole, filters);

      const pipelineColumns: PipelineColumn[] = [
        {
          id: 'scheduled',
          title: 'Scheduled',
          status: 'scheduled',
          color: 'bg-blue-50 border-blue-200',
          interviews: data.interviewsByStatus?.scheduled || []
        },
        {
          id: 'in-progress',
          title: 'In Progress',
          status: 'in-progress',
          color: 'bg-yellow-50 border-yellow-200',
          interviews: data.interviewsByStatus?.['in-progress'] || []
        },
        {
          id: 'completed',
          title: 'Completed',
          status: 'completed',
          color: 'bg-green-50 border-green-200',
          interviews: data.interviewsByStatus?.completed || []
        },
        {
          id: 'cancelled',
          title: 'Cancelled / No Show',
          status: 'cancelled',
          color: 'bg-red-50 border-red-200',
          interviews: data.interviewsByStatus?.cancelled || []
        }
      ];

      setColumns(pipelineColumns);
    } catch (error) {
      console.error('Failed to fetch interviews:', error);
      // Set empty columns on error
      setColumns([
        {
          id: 'scheduled',
          title: 'Scheduled',
          status: 'scheduled',
          color: 'bg-blue-50 border-blue-200',
          interviews: []
        },
        {
          id: 'in-progress',
          title: 'In Progress',
          status: 'in-progress',
          color: 'bg-yellow-50 border-yellow-200',
          interviews: []
        },
        {
          id: 'completed',
          title: 'Completed',
          status: 'completed',
          color: 'bg-green-50 border-green-200',
          interviews: []
        },
        {
          id: 'cancelled',
          title: 'Cancelled / No Show',
          status: 'cancelled',
          color: 'bg-red-50 border-red-200',
          interviews: []
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;
    
    if (source.droppableId === destination.droppableId) return;

    // Update interview status based on destination column
    const newStatus = destination.droppableId;
    console.log(`Moving interview ${draggableId} to ${newStatus}`);
    
    // Update local state
    setColumns(prev => {
      const newColumns = [...prev];
      const sourceColumn = newColumns.find(col => col.id === source.droppableId);
      const destColumn = newColumns.find(col => col.id === destination.droppableId);
      
      if (sourceColumn && destColumn) {
        const interview = sourceColumn.interviews.find(i => i.id === draggableId);
        if (interview) {
          sourceColumn.interviews = sourceColumn.interviews.filter(i => i.id !== draggableId);
          interview.status = newStatus as any;
          destColumn.interviews.splice(destination.index, 0, interview);
        }
      }
      
      return newColumns;
    });

    // API call to update interview status
    // updateInterviewStatus(draggableId, newStatus);
  };

  const handleInterviewSelect = (interviewId: string, isSelected: boolean) => {
    if (isSelected) {
      onSelectionChange([...selectedInterviews, interviewId]);
    } else {
      onSelectionChange(selectedInterviews.filter(id => id !== interviewId));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMeetingIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'phone': return <Phone className="h-4 w-4" />;
      case 'in-person': return <MapPin className="h-4 w-4" />;
      default: return <Video className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="h-96">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="h-24 bg-gray-100 rounded animate-pulse"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <TooltipProvider>
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {columns.map((column) => (
            <Card key={column.id} className={`${column.color} min-h-[500px]`}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{column.title}</span>
                  <Badge variant="secondary">{column.interviews.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`space-y-3 min-h-[400px] ${
                        snapshot.isDraggingOver ? 'bg-blue-50 rounded-lg p-2' : ''
                      }`}
                    >
                      {column.interviews.map((interview, index) => (
                        <Draggable key={interview.id} draggableId={interview.id} index={index}>
                          {(provided, snapshot) => (
                            <Card
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`cursor-move transition-shadow hover:shadow-md ${
                                snapshot.isDragging ? 'shadow-lg rotate-2' : ''
                              } ${
                                selectedInterviews.includes(interview.id) 
                                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                                  : 'bg-white'
                              }`}
                              onClick={() => handleInterviewSelect(
                                interview.id, 
                                !selectedInterviews.includes(interview.id)
                              )}
                            >
                              <CardContent className="p-4">
                                <div className="space-y-3">
                                  {/* Priority and Tags */}
                                  <div className="flex items-center justify-between">
                                    <Badge className={`text-xs ${getPriorityColor(interview.priority)}`}>
                                      {interview.priority}
                                    </Badge>
                                    <div className="flex gap-1">
                                      {interview.tags.slice(0, 2).map(tag => (
                                        <Badge key={tag} variant="outline" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>

                                  {/* Candidate Info */}
                                  <div className="flex items-center gap-3">
                                    <Avatar className="h-8 w-8">
                                      <AvatarImage src={interview.candidateAvatar} />
                                      <AvatarFallback>
                                        {interview.candidateName.split(' ').map(n => n[0]).join('')}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1 min-w-0">
                                      <p className="font-medium text-sm truncate">
                                        {interview.candidateName}
                                      </p>
                                      <p className="text-xs text-muted-foreground truncate">
                                        {interview.candidateEmail}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Role and Interview */}
                                  <div>
                                    <p className="font-medium text-sm">{interview.role}</p>
                                    <p className="text-xs text-muted-foreground">{interview.interviewName}</p>
                                  </div>

                                  {/* Schedule and Meeting Type */}
                                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {formatDate(interview.scheduleTime)}
                                    </div>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        {getMeetingIcon(interview.meetingType)}
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {interview.meetingType} interview
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>

                                  {/* Interviewer */}
                                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                    <User className="h-3 w-3" />
                                    {interview.interviewer}
                                  </div>

                                  {/* Score (for completed interviews) */}
                                  {interview.score && (
                                    <div className="flex items-center gap-1 text-xs">
                                      <Star className="h-3 w-3 text-yellow-500" />
                                      <span className="font-medium">{interview.score}/10</span>
                                    </div>
                                  )}

                                  {/* Action Buttons */}
                                  <div className="flex gap-2 pt-2">
                                    <Button size="sm" variant="outline" className="flex-1 text-xs">
                                      View
                                    </Button>
                                    {interview.status === 'scheduled' && (
                                      <Button size="sm" variant="outline" className="flex-1 text-xs">
                                        Join
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </CardContent>
            </Card>
          ))}
        </div>
      </DragDropContext>
    </TooltipProvider>
  );
};
