'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@camped-ui/collapsible';
import { Calendar, Clock, FileText, User, ArrowRight, Briefcase, Video, ChevronLeft, X } from 'lucide-react';
import { timeAgo } from '@/utils/time-ago';

interface Interview {
  id: string;
  role: string;
  level: string;
  event: string;
  finalScore?: number;
  resumeScore?: number;
  interviewStatus: string;
  completedTime?: string;
  createdAt: string;
  scheduleTime?: string;
  feedback?: any; // Complete feedback JSON object
  eventDetails?: {
    id: string;
    name: string;
    role: string;
    level: string;
    isAiQuestion: boolean;
    isPlacement: boolean;
  };
  type: 'career_practice' | 'video_meeting';
  interviewType: string;
  meetingType?: string;
  careerPracticeId?: string;
}

interface CandidateOtherInterviewsProps {
  candidateId: string;
  organizationId: string;
  currentInterviewId: string;
  currentOrgId?: string;
  currentOrgRole?: string;
}

export const CandidateOtherInterviews: React.FC<CandidateOtherInterviewsProps> = ({
  candidateId,
  organizationId,
  currentInterviewId,
  currentOrgId,
  currentOrgRole,
}) => {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const fetchOtherInterviews = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          candidateId,
          organizationId,
          currentInterviewId,
          ...(currentOrgId && { membershipId: currentOrgId }),
          ...(currentOrgRole && { role: currentOrgRole }),
        });

        const response = await fetch(`/api/admin/get-candidate-other-interviews?${params}`);
        const data = await response.json();

        if (response.ok) {
          setInterviews(data.interviews || []);
        } else {
          setError(data.message || 'Failed to fetch interviews');
        }
      } catch (err) {
        setError('Error loading interviews');
        console.error('Error fetching other interviews:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOtherInterviews();
  }, [candidateId, organizationId, currentInterviewId, currentOrgId, currentOrgRole]);

  const getStatusBadge = (status: string, recommendation?: string, type?: string) => {
    // Handle video meeting statuses
    if (type === 'video_meeting') {
      if (status === 'COMPLETED') {
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      } else if (status === 'PENDING') {
        return <Badge className="bg-yellow-100 text-yellow-800">Scheduled</Badge>;
      } else if (status === 'CANCELLED') {
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      }
      return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }

    // Handle career practice statuses
    if (status === 'COMPLETED') {
      if (recommendation === 'shortlisted') {
        return <Badge className="bg-green-100 text-green-800">Shortlisted</Badge>;
      } else if (recommendation === 'rejected') {
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      }
      return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
    } else if (status === 'PARTIALLY_COMPLETED') {
      return <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
    }
    return <Badge className="bg-gray-100 text-gray-800">Not Started</Badge>;
  };

  const getInterviewTypeIcon = (interview: Interview) => {
    if (interview.type === 'video_meeting') {
      return <Video className="h-4 w-4 text-green-600" />;
    } else if (interview.eventDetails?.isAiQuestion) {
      return <User className="h-4 w-4 text-purple-600" />;
    } else if (interview.eventDetails?.isPlacement) {
      return <Briefcase className="h-4 w-4 text-blue-600" />;
    }
    return <FileText className="h-4 w-4 text-gray-600" />;
  };

  const getInterviewLink = (interview: Interview) => {
    if (interview.type === 'video_meeting') {
      return `/video-interviews/${interview.id}`;
    } else if (interview.careerPracticeId) {
      // If it's linked to a career practice, use the direct interview route
      return `/interviews/${interview.eventDetails?.id}/${interview.careerPracticeId}`;
    } else {
      // Default to the direct interview detail route
      return `/interviews/${interview.eventDetails?.id}/${interview.id}`;
    }
  };

  if (loading) {
    return (
      <div className="fixed right-0 top-0 h-full z-40">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          {!isOpen && (
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full rounded-r-none rounded-l-lg border-r-0 bg-white shadow-lg hover:bg-gray-50 flex flex-col items-center justify-center h-24 w-12 p-0"
              >
                <User className="h-4 w-4 mb-1" />
                <div className="text-xs text-gray-500 mb-1">...</div>
                <ChevronLeft className="h-3 w-3" />
              </Button>
            </CollapsibleTrigger>
          )}
          <CollapsibleContent>
            <div className="h-full w-80 bg-white border-l border-gray-200 shadow-xl overflow-hidden flex flex-col">
              <div className="p-4 border-b border-gray-100 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    <span className="font-medium">Other Interviews</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 hover:bg-gray-200"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-gray-500 text-sm">Loading...</div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed right-0 top-0 h-full z-40">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          {!isOpen && (
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full rounded-r-none rounded-l-lg border-r-0 bg-white shadow-lg hover:bg-gray-50 flex flex-col items-center justify-center h-24 w-12 p-0"
              >
                <User className="h-4 w-4 mb-1" />
                <div className="text-xs text-red-500 mb-1">!</div>
                <ChevronLeft className="h-3 w-3" />
              </Button>
            </CollapsibleTrigger>
          )}
          <CollapsibleContent>
            <div className="h-full w-80 bg-white border-l border-gray-200 shadow-xl overflow-hidden flex flex-col">
              <div className="p-4 border-b border-gray-100 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    <span className="font-medium">Other Interviews</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 hover:bg-gray-200"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex-1 flex items-center justify-center p-4">
                <div className="text-red-500 text-sm text-center">{error}</div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  }

  if (interviews.length === 0) {
    return (
      <div className="fixed right-0 top-0 h-full z-40">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          {!isOpen && (
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full rounded-r-none rounded-l-lg border-r-0 bg-white shadow-lg hover:bg-gray-50 flex flex-col items-center justify-center h-24 w-12 p-0"
              >
                <User className="h-4 w-4 mb-1" />
                <div className="text-xs text-gray-500 mb-1">0</div>
                <ChevronLeft className="h-3 w-3" />
              </Button>
            </CollapsibleTrigger>
          )}
          <CollapsibleContent>
            <div className="h-full w-80 bg-white border-l border-gray-200 shadow-xl overflow-hidden flex flex-col">
              <div className="p-4 border-b border-gray-100 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    <span className="font-medium">Other Interviews</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 hover:bg-gray-200"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="flex-1 flex items-center justify-center p-4">
                <div className="text-center">
                  <User className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-gray-500 text-sm">No other interviews found</div>
                  <div className="text-xs text-gray-400 mt-1">
                    This candidate hasn't taken any other interviews in this organization
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  }

  return (
    <div className="fixed right-0 top-0 h-full z-40">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        {!isOpen && (
          <CollapsibleTrigger asChild>
            <Button
              variant="outline"
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full rounded-r-none rounded-l-lg border-r-0 bg-white shadow-lg hover:bg-gray-50 flex flex-col items-center justify-center h-24 w-12 p-0"
            >
              <User className="h-4 w-4 mb-1" />
              <Badge variant="secondary" className="text-xs mb-1 min-w-0 h-4 px-1">
                {interviews.length}
              </Badge>
              <ChevronLeft className="h-3 w-3" />
            </Button>
          </CollapsibleTrigger>
        )}
        <CollapsibleContent>
          <div className="h-full w-80 bg-white border-l border-gray-200 shadow-xl overflow-hidden flex flex-col">
            <div className="p-4 border-b border-gray-100 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  <span className="font-medium">Other Interviews</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {interviews.length}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 hover:bg-gray-200"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                Previous interviews and meetings for this candidate
              </p>
            </div>
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {interviews.map((interview) => (
            <div
              key={interview.id}
              className="rounded-lg border border-gray-200 p-3 hover:bg-gray-50 transition-colors bg-white"
            >
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  {getInterviewTypeIcon(interview)}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 truncate">
                      {interview.eventDetails?.name || interview.event}
                    </h4>
                    <p className="text-xs text-gray-600 truncate">
                      {interview.eventDetails?.role || interview.role} • {interview.eventDetails?.level || interview.level}
                    </p>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Calendar className="h-3 w-3" />
                    {timeAgo(interview.createdAt)}
                  </div>
                  {interview.completedTime && (
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      Completed {timeAgo(interview.completedTime)}
                    </div>
                  )}
                  {(interview.finalScore || interview.resumeScore) && (
                    <div className="text-xs font-medium text-gray-700">
                      Score: {interview.finalScore || interview.resumeScore}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {getStatusBadge(
                      interview.interviewStatus,
                      interview.feedback?.overall_recommendation?.decision,
                      interview.type
                    )}
                    {interview.type === 'video_meeting' && (
                      <Badge variant="outline" className="text-xs text-green-600 border-green-200">
                        Video
                      </Badge>
                    )}
                    {interview.eventDetails?.isAiQuestion && (
                      <Badge variant="outline" className="text-xs text-purple-600 border-purple-200">
                        AI
                      </Badge>
                    )}
                  </div>

                  <Link href={getInterviewLink(interview)} target="_blank" rel="noopener noreferrer">
                    <Button variant="outline" size="sm" className="h-6 px-2 text-xs">
                      View
                      <ArrowRight className="h-2 w-2 ml-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};
