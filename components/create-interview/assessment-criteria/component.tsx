'use client';

import { useState, useEffect, useCallback } from 'react';

import { ContextualHelp } from '@/components/create-interview/contextual-help';
import { Icon } from '@/icons';
import { CRITERIA_CATEGORIES, COMMON_SCALES, PREDEFINED_SCALE_OPTIONS } from '@/types/assessment';
import type { AssessmentCriteria, AssessmentTemplate } from '@/types/assessment';

import { Button } from '@camped-ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';
import { Textarea } from '@camped-ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@camped-ui/select';
import { Switch } from '@camped-ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';
import { Checkbox } from '@camped-ui/checkbox';

import { useInterviewForm } from '../provider';

export const AssessmentCriteriaConfig = () => {
  const context = useInterviewForm();
  const roleForm = (context as any)?.roleForm;
  const form = (context as any)?.assessmentCriteriaForm;
  const [selectedCriteria, setSelectedCriteria] = useState<AssessmentCriteria[]>([]);
  const [availableCriteriaGroups, setAvailableCriteriaGroups] = useState<Record<string, AssessmentCriteria[]>>({});
  const [isLoadingCriteria, setIsLoadingCriteria] = useState(false);

  const watchUsePrebuilt = form.watch('usePrebuilt');
  const watchSelectedCriteriaIds = form.watch('selectedCriteriaIds') || [];
  const watchCustomCriteria = form.watch('customCriteria');

  // Fetch available criteria from predefined groups
  useEffect(() => {
    const fetchCriteria = async () => {
      setIsLoadingCriteria(true);
      try {
        // Load predefined criteria groups from templates
        const response = await fetch('/api/assessment-templates');
        if (response.ok) {
          const templates = await response.json();

          // Group criteria by category for easy selection
          const criteriaGroups: Record<string, AssessmentCriteria[]> = {};
          templates.forEach((template: AssessmentTemplate) => {
            const groupName = template.name;
            criteriaGroups[groupName] = template.criteria as AssessmentCriteria[];
          });

          setAvailableCriteriaGroups(criteriaGroups);
        }
      } catch (error) {
        console.error('Error fetching assessment criteria:', error);
      } finally {
        setIsLoadingCriteria(false);
      }
    };

    fetchCriteria();
  }, []);

  // Auto-suggest criteria based on role (only run once when criteria groups are loaded)
  useEffect(() => {
    if (Object.keys(availableCriteriaGroups).length === 0) return;

    // Only auto-suggest if no criteria are already selected
    const currentIds = form.getValues('selectedCriteriaIds') || [];
    if (currentIds.length > 0) return;

    const role = roleForm?.getValues('role')?.toLowerCase() || '';
    const suggestedCriteriaIds: string[] = [];

    // Auto-suggest technical criteria for technical roles
    if (role.includes('engineer') || role.includes('developer') || role.includes('programmer')) {
      const techCriteria = availableCriteriaGroups['Technical Roles Assessment'];
      if (techCriteria) {
        suggestedCriteriaIds.push(...techCriteria.map(c => c.id));
      }
    }

    // Auto-suggest sales criteria for sales roles
    if (role.includes('sales') || role.includes('account')) {
      const salesCriteria = availableCriteriaGroups['Sales Roles Assessment'];
      if (salesCriteria) {
        suggestedCriteriaIds.push(...salesCriteria.map(c => c.id));
      }
    }

    // Auto-suggest leadership criteria for management roles
    if (role.includes('manager') || role.includes('director') || role.includes('lead')) {
      const leadershipCriteria = availableCriteriaGroups['Leadership Assessment'];
      if (leadershipCriteria) {
        suggestedCriteriaIds.push(...leadershipCriteria.map(c => c.id));
      }
    }

    // Always suggest communication criteria for international roles
    if (role.includes('international') || role.includes('global') || role.includes('remote')) {
      const languageCriteria = availableCriteriaGroups['Language Proficiency Assessment'];
      if (languageCriteria) {
        suggestedCriteriaIds.push(...languageCriteria.map(c => c.id));
      }
    }

    if (suggestedCriteriaIds.length > 0) {
      form.setValue('selectedCriteriaIds', suggestedCriteriaIds);
    }
  }, [availableCriteriaGroups]); // Only depend on availableCriteriaGroups

  // Update selected criteria when IDs change
  useEffect(() => {
    if (watchSelectedCriteriaIds && watchSelectedCriteriaIds.length > 0) {
      updateSelectedCriteria(watchSelectedCriteriaIds);
    } else {
      setSelectedCriteria([]);
    }
  }, [watchSelectedCriteriaIds, availableCriteriaGroups]);

  const updateSelectedCriteria = useCallback((criteriaIds: string[]) => {
    const allCriteria = Object.values(availableCriteriaGroups).flat();
    const selected = allCriteria.filter(criteria => criteriaIds.includes(criteria.id));
    setSelectedCriteria(selected);
  }, [availableCriteriaGroups]);

  const addCustomCriteria = () => {
    const newCriteria: AssessmentCriteria = {
      id: `custom_${Date.now()}`,
      name: '',
      description: '',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 10,
      category: 'custom',
      isRequired: true,
    };
    
    const currentCriteria = form.getValues('customCriteria');
    form.setValue('customCriteria', [...currentCriteria, newCriteria]);
  };

  const removeCustomCriteria = (index: number) => {
    const currentCriteria = form.getValues('customCriteria');
    form.setValue('customCriteria', currentCriteria.filter((_, i) => i !== index));
  };

  const getTotalWeight = () => {
    return watchCustomCriteria.reduce((sum, criteria) => sum + (criteria.weight || 0), 0);
  };

  const getWeightColor = () => {
    const total = getTotalWeight();
    if (total === 100) return 'text-green-600';
    if (total > 100) return 'text-red-600';
    return 'text-yellow-600';
  };

  return (
    <div className="w-full px-1">
      <div className="mb-6">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Assessment Criteria Configuration</h3>
          <ContextualHelp
            title="Assessment Criteria"
            content="Define what you want to measure in this interview. You can use predefined templates or create custom criteria with specific scales and weights."
          />
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Configure how candidates will be evaluated during the interview
        </p>
      </div>

      <Form {...form}>
        <div className="space-y-6">
        {/* Criteria Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="CheckSquare" className="h-5 w-5" />
              Assessment Criteria Selection
            </CardTitle>
            <CardDescription>
              Choose multiple assessment criteria from different categories. You can combine technical, language, sales, and other criteria as needed.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="usePrebuilt"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <div>
                    <FormLabel>Use Predefined Criteria</FormLabel>
                    <p className="text-sm text-gray-500">
                      Select from our curated assessment criteria library
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked);
                        if (!checked) {
                          form.setValue('selectedCriteriaIds', []);
                          setSelectedCriteria([]);
                        }
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {watchUsePrebuilt && (
              <div className="space-y-4">
                <div className="text-sm font-medium">Available Assessment Criteria</div>
                {isLoadingCriteria ? (
                  <div className="text-center py-4 text-gray-500">Loading criteria...</div>
                ) : (
                  <div className="space-y-4">
                    {Object.entries(availableCriteriaGroups).map(([groupName, criteria]) => (
                      <div key={groupName} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">{groupName}</h4>
                          <div className="flex items-center gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const currentIds = form.getValues('selectedCriteriaIds') || [];
                                const groupIds = criteria.map(c => c.id);
                                const allSelected = groupIds.every(id => currentIds.includes(id));

                                if (allSelected) {
                                  // Deselect all from this group
                                  const newIds = currentIds.filter(id => !groupIds.includes(id));
                                  form.setValue('selectedCriteriaIds', newIds);
                                } else {
                                  // Select all from this group
                                  const newIds = [...new Set([...currentIds, ...groupIds])];
                                  form.setValue('selectedCriteriaIds', newIds);
                                }
                              }}
                            >
                              {criteria.every(c => watchSelectedCriteriaIds.includes(c.id)) ? 'Deselect All' : 'Select All'}
                            </Button>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 gap-2">
                          {criteria.map((criterion) => (
                            <FormField
                              key={criterion.id}
                              control={form.control}
                              name="selectedCriteriaIds"
                              render={({ field }) => (
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <Checkbox
                                      checked={field.value?.includes(criterion.id) || false}
                                      onCheckedChange={(checked) => {
                                        const currentIds = field.value || [];
                                        if (checked) {
                                          field.onChange([...currentIds, criterion.id]);
                                        } else {
                                          field.onChange(currentIds.filter((id: string) => id !== criterion.id));
                                        }
                                      }}
                                    />
                                  </FormControl>
                                  <div className="flex-1">
                                    <FormLabel className="text-sm font-medium cursor-pointer">
                                      {criterion.name}
                                    </FormLabel>
                                    <p className="text-xs text-gray-500">{criterion.description}</p>
                                    {criterion.scale?.description && (
                                      <p className="text-xs text-blue-600 mt-1">
                                        <span className="font-medium">Scale:</span> {criterion.scale.description}
                                      </p>
                                    )}
                                    <div className="flex items-center gap-2 mt-1">
                                      <Badge variant="outline" className="text-xs">{criterion.weight}%</Badge>
                                      <Badge variant="secondary" className="text-xs">{criterion.category}</Badge>
                                    </div>
                                  </div>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {selectedCriteria.length > 0 && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-900 mb-2">Selected Assessment Criteria ({selectedCriteria.length})</h4>
                <div className="space-y-3">
                  {selectedCriteria.map((criteria) => (
                    <div key={criteria.id} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{criteria.name}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{criteria.weight}%</Badge>
                          <Badge variant="secondary">{criteria.category}</Badge>
                        </div>
                      </div>
                      {criteria.scale?.description && (
                        <p className="text-xs text-blue-600 pl-2">
                          <span className="font-medium">Scale:</span> {criteria.scale.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-green-200">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium text-green-800">Total Weight:</span>
                    <span className={`font-bold ${selectedCriteria.reduce((sum, c) => sum + c.weight, 0) === 100 ? 'text-green-600' : 'text-yellow-600'}`}>
                      {selectedCriteria.reduce((sum, c) => sum + c.weight, 0)}%
                    </span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Custom Criteria Section */}
        {!watchUsePrebuilt && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon name="Settings" className="h-5 w-5" />
                  Custom Assessment Criteria
                </div>
                <div className={`text-sm font-medium ${getWeightColor()}`}>
                  Total Weight: {getTotalWeight()}%
                </div>
              </CardTitle>
              <CardDescription>
                Create your own assessment criteria with custom scales and weights
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {watchCustomCriteria.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Icon name="Plus" className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No custom criteria added yet</p>
                  <p className="text-sm">Click "Add Criteria" to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {watchCustomCriteria.map((criteria, index) => (
                    <CustomCriteriaItem
                      key={criteria.id}
                      criteria={criteria}
                      index={index}
                      form={form}
                      onRemove={() => removeCustomCriteria(index)}
                    />
                  ))}
                </div>
              )}

              <Button
                type="button"
                variant="outline"
                onClick={addCustomCriteria}
                className="w-full"
              >
                <Icon name="Plus" className="h-4 w-4 mr-2" />
                Add Assessment Criteria
              </Button>

              {getTotalWeight() !== 100 && watchCustomCriteria.length > 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <Icon name="AlertTriangle" className="h-4 w-4 inline mr-1" />
                    Total weight should equal 100%. Current total: {getTotalWeight()}%
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
        </div>
      </Form>
    </div>
  );
};

// Custom Criteria Item Component
const CustomCriteriaItem = ({ criteria, index, form, onRemove }: {
  criteria: AssessmentCriteria;
  index: number;
  form: any;
  onRemove: () => void;
}) => {
  const updateCriteria = (field: string, value: any) => {
    const currentCriteria = form.getValues('customCriteria');
    const updatedCriteria = [...currentCriteria];
    updatedCriteria[index] = { ...updatedCriteria[index], [field]: value };
    form.setValue('customCriteria', updatedCriteria);
  };

  const updateScale = (scaleField: string, value: any) => {
    const currentCriteria = form.getValues('customCriteria');
    const updatedCriteria = [...currentCriteria];
    updatedCriteria[index] = {
      ...updatedCriteria[index],
      scale: { ...updatedCriteria[index].scale, [scaleField]: value }
    };
    form.setValue('customCriteria', updatedCriteria);
  };

  const getCurrentScaleOption = (scale: any): string => {
    // Find matching predefined scale option
    for (const option of PREDEFINED_SCALE_OPTIONS) {
      if (!option.scale) continue;

      if (option.scale.type === scale.type) {
        if (scale.type === 'numeric' && option.scale.type === 'numeric' &&
            (option.scale as any).min === scale.min &&
            (option.scale as any).max === scale.max) {
          return option.id;
        } else if (scale.type === 'categorical' && option.scale.type === 'categorical' &&
                   JSON.stringify((option.scale as any).labels) === JSON.stringify(scale.labels)) {
          return option.id;
        } else if (scale.type === 'boolean' && option.scale.type === 'boolean') {
          return option.id;
        }
      }
    }
    return 'custom';
  };

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <div className="flex items-center justify-between">
        <h5 className="font-medium">Criteria {index + 1}</h5>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-red-600 hover:text-red-700"
        >
          <Icon name="Trash2" className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Criteria Name */}
        <div>
          <label className="text-sm font-medium">Criteria Name</label>
          <Input
            value={criteria.name}
            onChange={(e) => updateCriteria('name', e.target.value)}
            placeholder="e.g., Problem Solving"
            className="mt-1"
          />
        </div>

        {/* Weight */}
        <div>
          <label className="text-sm font-medium">Weight (%)</label>
          <Input
            type="number"
            min="1"
            max="100"
            value={criteria.weight}
            onChange={(e) => updateCriteria('weight', parseInt(e.target.value) || 0)}
            className="mt-1"
          />
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="text-sm font-medium">Description</label>
        <Textarea
          value={criteria.description}
          onChange={(e) => updateCriteria('description', e.target.value)}
          placeholder="Describe what this criteria measures..."
          className="mt-1"
          rows={2}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Category */}
        <div>
          <label className="text-sm font-medium">Category</label>
          <Select
            value={criteria.category}
            onValueChange={(value) => updateCriteria('category', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CRITERIA_CATEGORIES.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  <div>
                    <div className="font-medium">{category.label}</div>
                    <div className="text-sm text-gray-500">{category.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Predefined Scale Options */}
        <div>
          <label className="text-sm font-medium">Scale Type</label>
          <Select
            value={getCurrentScaleOption(criteria.scale)}
            onValueChange={(value) => {
              const selectedOption = PREDEFINED_SCALE_OPTIONS.find(opt => opt.id === value);
              if (selectedOption && selectedOption.scale) {
                // Apply predefined scale
                updateScale('type', selectedOption.scale.type);
                updateScale('description', selectedOption.scale.description);
                if (selectedOption.scale.type === 'numeric') {
                  updateScale('min', selectedOption.scale.min);
                  updateScale('max', selectedOption.scale.max);
                  updateScale('labels', undefined);
                } else if (selectedOption.scale.type === 'categorical') {
                  updateScale('labels', selectedOption.scale.labels);
                  updateScale('min', undefined);
                  updateScale('max', undefined);
                } else if (selectedOption.scale.type === 'boolean') {
                  updateScale('min', undefined);
                  updateScale('max', undefined);
                  updateScale('labels', undefined);
                }
              } else if (value === 'custom') {
                // Reset to basic numeric for custom
                updateScale('type', 'numeric');
                updateScale('min', 1);
                updateScale('max', 10);
                updateScale('labels', undefined);
                updateScale('description', '');
              }
            }}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {PREDEFINED_SCALE_OPTIONS.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  <div>
                    <div className="font-medium">{option.name}</div>
                    <div className="text-sm text-gray-500">{option.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Scale Configuration - Only show for custom scales */}
      {getCurrentScaleOption(criteria.scale) === 'custom' && (
        <div className="space-y-4">
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h5 className="font-medium text-blue-900 mb-2">Custom Scale Configuration</h5>
            <p className="text-sm text-blue-700">
              Configure your custom scale settings below.
            </p>
          </div>

          {/* Scale Type for Custom */}
          <div>
            <label className="text-sm font-medium">Scale Type</label>
            <Select
              value={criteria.scale.type}
              onValueChange={(value) => {
                if (value === 'numeric') {
                  updateScale('type', value);
                  updateScale('min', 1);
                  updateScale('max', 10);
                  updateScale('labels', undefined);
                } else if (value === 'categorical') {
                  updateScale('type', value);
                  updateScale('min', undefined);
                  updateScale('max', undefined);
                  updateScale('labels', ['Poor', 'Good', 'Excellent']);
                } else if (value === 'boolean') {
                  updateScale('type', value);
                  updateScale('min', undefined);
                  updateScale('max', undefined);
                  updateScale('labels', undefined);
                }
              }}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="numeric">Numeric Scale</SelectItem>
                <SelectItem value="categorical">Categorical Scale</SelectItem>
                <SelectItem value="boolean">Yes/No Scale</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {criteria.scale.type === 'numeric' && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Min Value</label>
                <Input
                  type="number"
                  value={criteria.scale.min || 1}
                  onChange={(e) => updateScale('min', parseInt(e.target.value) || 1)}
                  className="mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Max Value</label>
                <Input
                  type="number"
                  value={criteria.scale.max || 10}
                  onChange={(e) => updateScale('max', parseInt(e.target.value) || 10)}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          {criteria.scale.type === 'categorical' && (
            <div>
              <label className="text-sm font-medium">Scale Labels (comma-separated)</label>
              <Input
                value={criteria.scale.labels?.join(', ') || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow typing commas and split only when user finishes typing
                  const labels = value.split(',').map(s => s.trim()).filter(Boolean);
                  updateScale('labels', labels);
                }}
                placeholder="e.g., Poor, Average, Good, Excellent"
                className="mt-1"
                onKeyDown={(e) => {
                  // Allow commas to be typed
                  if (e.key === ',') {
                    e.stopPropagation();
                  }
                }}
              />
              <p className="text-xs text-gray-500 mt-1">
                Separate labels with commas. Examples: "Poor, Good, Excellent" or "A1, A2, B1, B2, C1, C2"
              </p>
            </div>
          )}
        </div>
      )}

      {/* Show scale preview for predefined scales */}
      {getCurrentScaleOption(criteria.scale) !== 'custom' && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <h5 className="font-medium text-green-900 mb-2">Scale Preview</h5>
          <p className="text-sm text-green-700">
            {criteria.scale.description}
          </p>
          {criteria.scale.labels && (
            <div className="mt-2">
              <span className="text-xs font-medium text-green-800">Options: </span>
              <span className="text-xs text-green-700">{criteria.scale.labels.join(', ')}</span>
            </div>
          )}
        </div>
      )}

      {/* Required Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <label className="text-sm font-medium">Required Criteria</label>
          <p className="text-xs text-gray-500">Must be evaluated for all candidates</p>
        </div>
        <Switch
          checked={criteria.isRequired}
          onCheckedChange={(checked) => updateCriteria('isRequired', checked)}
        />
      </div>
    </div>
  );
};
