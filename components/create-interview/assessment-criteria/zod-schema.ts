import { z } from 'zod';

const assessmentScaleSchema = z.object({
  type: z.enum(['numeric', 'categorical', 'boolean']),
  min: z.number().optional(),
  max: z.number().optional(),
  labels: z.array(z.string()).optional(),
  description: z.string().optional(),
});

const assessmentCriteriaSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Criteria name is required'),
  description: z.string().min(1, 'Description is required'),
  scale: assessmentScaleSchema,
  weight: z.number().min(1).max(100),
  category: z.enum(['technical', 'communication', 'behavioral', 'domain_specific', 'custom']),
  isRequired: z.boolean(),
});

export const assessmentCriteriaConfigSchema = z.object({
  usePrebuilt: z.boolean().default(false),
  selectedCriteriaIds: z.array(z.string()).default([]),
  customCriteria: z.array(assessmentCriteriaSchema).default([]),
  overallScoreCalculation: z.enum(['weighted_average', 'custom']).default('weighted_average'),
  passingThreshold: z.number().min(0).max(100).optional(),
}).refine((data) => {
  // If using prebuilt, must have at least one selected criteria
  if (data.usePrebuilt && data.selectedCriteriaIds.length === 0) {
    return false;
  }
  // If not using prebuilt, must have at least one custom criteria
  if (!data.usePrebuilt && data.customCriteria.length === 0) {
    return false;
  }
  // Total weight should be 100 if using custom criteria
  if (!data.usePrebuilt && data.customCriteria.length > 0) {
    const totalWeight = data.customCriteria.reduce((sum, criteria) => sum + criteria.weight, 0);
    return Math.abs(totalWeight - 100) <= 1; // Allow 1% tolerance
  }
  return true;
}, {
  message: 'Either select predefined criteria or add custom criteria with total weight of 100%',
});

export type AssessmentCriteriaConfigFormData = z.infer<typeof assessmentCriteriaConfigSchema>;
