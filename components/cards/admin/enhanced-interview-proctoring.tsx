'use client';

import React, { useState } from 'react';
import { Icon } from '@/icons';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Separator } from '@camped-ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@camped-ui/tabs';

interface EnhancedProctoringData {
  timestamp?: number;
  eyeTracking?: {
    lookAwayCount: number;
    attentionScore: number;
    averageGazeDeviation: number;
  };
  multiPerson?: {
    violations: number;
    personCount: number;
    additionalPeopleDetected: boolean;
  };
  voiceAnalysis?: {
    backgroundNoise: number;
    suspiciousAudio: boolean;
    voicePatternChanges: number;
  };
  emotionAnalysis?: {
    stressLevel: number;
    emotionChanges: number;
    suspiciousBehavior: boolean;
  };
  objectDetection?: {
    prohibitedItems: string[];
    itemDetectionCount: number;
  };
  // Legacy fields
  tabSwitch?: number;
  fullScreen?: number;
  prohibitedItems?: number;
  multiplePeople?: number;
  suspiciousAudio?: number;
  attentionLoss?: number;
}

interface EnhancedInterviewProctoringCardProps {
  title: string;
  proctorWarnings: EnhancedProctoringData;
}

export const EnhancedInterviewProctoringCard: React.FC<EnhancedInterviewProctoringCardProps> = ({
  title,
  proctorWarnings,
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Dynamic color scheme based on risk level
  const getProctoringColors = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low':
      case 'minimal':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/50',
          text: 'text-green-600',
          icon: 'text-green-600',
          badge: 'bg-green-500',
        };
      case 'medium':
        return {
          border: 'border-yellow-200',
          bg: 'bg-yellow-50/50',
          text: 'text-yellow-600',
          icon: 'text-yellow-600',
          badge: 'bg-yellow-500',
        };
      case 'high':
        return {
          border: 'border-orange-200',
          bg: 'bg-orange-50/50',
          text: 'text-orange-600',
          icon: 'text-orange-600',
          badge: 'bg-orange-500',
        };
      case 'critical':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/50',
          text: 'text-red-600',
          icon: 'text-red-600',
          badge: 'bg-red-500',
        };
      default:
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50/50',
          text: 'text-gray-600',
          icon: 'text-gray-600',
          badge: 'bg-gray-500',
        };
    }
  };

  // Calculate overall risk score
  const calculateRiskScore = (): { score: number; level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'; color: string } => {
    let riskScore = 0;

    // Legacy violations
    if (proctorWarnings?.tabSwitch) riskScore += proctorWarnings.tabSwitch * 2;
    if (proctorWarnings?.fullScreen) riskScore += proctorWarnings.fullScreen * 2;
    if (proctorWarnings?.prohibitedItems) riskScore += proctorWarnings.prohibitedItems * 5;
    if (proctorWarnings?.multiplePeople) riskScore += proctorWarnings.multiplePeople * 8;

    // Enhanced violations
    if ((proctorWarnings?.eyeTracking?.attentionScore ?? 100) < 70) riskScore += 3;
    if ((proctorWarnings?.eyeTracking?.lookAwayCount ?? 0) > 10) riskScore += 2;
    if ((proctorWarnings?.objectDetection?.prohibitedItems?.length ?? 0) > 0) riskScore += (proctorWarnings?.objectDetection?.prohibitedItems?.length ?? 0) * 5;
    if (proctorWarnings?.multiPerson?.additionalPeopleDetected) riskScore += 8;
    if (proctorWarnings?.voiceAnalysis?.suspiciousAudio) riskScore += 4;
    if ((proctorWarnings?.voiceAnalysis?.backgroundNoise ?? 0) > 50) riskScore += 2;
    if (proctorWarnings?.emotionAnalysis?.suspiciousBehavior) riskScore += 6;
    if ((proctorWarnings?.emotionAnalysis?.stressLevel ?? 0) > 80) riskScore += 3;

    if (riskScore >= 20) return { score: riskScore, level: 'CRITICAL', color: getProctoringColors('critical').badge };
    if (riskScore >= 12) return { score: riskScore, level: 'HIGH', color: getProctoringColors('high').badge };
    if (riskScore >= 6) return { score: riskScore, level: 'MEDIUM', color: getProctoringColors('medium').badge };
    return { score: riskScore, level: 'LOW', color: getProctoringColors('low').badge };
  };

  const riskAssessment = calculateRiskScore();
  const riskColors = getProctoringColors(riskAssessment.level);

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  };

  const getAttentionScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStressLevelColor = (level: number) => {
    if (level <= 30) return 'text-green-600';
    if (level <= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className={`w-full border-2 ${riskColors.border} ${riskColors.bg}`}>
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className={`text-xl ${riskColors.text}`}>{title}</CardTitle>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${riskAssessment.color}`} />
            <Badge variant={riskAssessment.level === 'LOW' ? 'default' : 'destructive'}>
              {riskAssessment.level} RISK
            </Badge>
          </div>
        </div>
        {proctorWarnings?.timestamp && (
          <CardDescription className="text-sm text-gray-500">
            Last updated: {formatTimestamp(proctorWarnings.timestamp)}
          </CardDescription>
        )}
      </CardHeader>
      <Separator />
      
      <CardContent className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="behavior">Behavior</TabsTrigger>
            <TabsTrigger value="environment">Environment</TabsTrigger>
            <TabsTrigger value="violations">Violations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon name="Eye" className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Attention Score</span>
                </div>
                <div className={`text-2xl font-bold ${getAttentionScoreColor(proctorWarnings?.eyeTracking?.attentionScore || 0)}`}>
                  {proctorWarnings?.eyeTracking?.attentionScore || 0}%
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon name="Users" className="h-4 w-4 text-purple-500" />
                  <span className="text-sm font-medium">Person Count</span>
                </div>
                <div className="text-2xl font-bold">
                  {proctorWarnings?.multiPerson?.personCount || 1}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon name="Volume2" className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">Background Noise</span>
                </div>
                <div className="text-2xl font-bold">
                  {proctorWarnings?.voiceAnalysis?.backgroundNoise || 0}dB
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Icon name="Heart" className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium">Stress Level</span>
                </div>
                <div className={`text-2xl font-bold ${getStressLevelColor(proctorWarnings?.emotionAnalysis?.stressLevel || 0)}`}>
                  {proctorWarnings?.emotionAnalysis?.stressLevel || 0}%
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="behavior" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Eye" className="h-4 w-4" />
                  <span className="font-medium">Look Away Count</span>
                </div>
                <span className="font-bold">{proctorWarnings?.eyeTracking?.lookAwayCount || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Activity" className="h-4 w-4" />
                  <span className="font-medium">Gaze Deviation</span>
                </div>
                <span className="font-bold">{proctorWarnings?.eyeTracking?.averageGazeDeviation || 0}°</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="TrendingUp" className="h-4 w-4" />
                  <span className="font-medium">Emotion Changes</span>
                </div>
                <span className="font-bold">{proctorWarnings?.emotionAnalysis?.emotionChanges || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="AlertTriangle" className="h-4 w-4" />
                  <span className="font-medium">Suspicious Behavior</span>
                </div>
                <Badge variant={proctorWarnings?.emotionAnalysis?.suspiciousBehavior ? 'destructive' : 'default'}>
                  {proctorWarnings?.emotionAnalysis?.suspiciousBehavior ? 'Detected' : 'None'}
                </Badge>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="environment" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Smartphone" className="h-4 w-4" />
                  <span className="font-medium">Prohibited Items</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-bold">{proctorWarnings?.objectDetection?.prohibitedItems?.length || 0}</span>
                  {(proctorWarnings?.objectDetection?.prohibitedItems?.length || 0) > 0 && (
                    <Badge variant="destructive">
                      {proctorWarnings?.objectDetection?.prohibitedItems?.join(', ') || ''}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Volume2" className="h-4 w-4" />
                  <span className="font-medium">Voice Pattern Changes</span>
                </div>
                <span className="font-bold">{proctorWarnings?.voiceAnalysis?.voicePatternChanges || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Mic" className="h-4 w-4" />
                  <span className="font-medium">Suspicious Audio</span>
                </div>
                <Badge variant={proctorWarnings?.voiceAnalysis?.suspiciousAudio ? 'destructive' : 'default'}>
                  {proctorWarnings?.voiceAnalysis?.suspiciousAudio ? 'Detected' : 'None'}
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Icon name="Users" className="h-4 w-4" />
                  <span className="font-medium">Additional People</span>
                </div>
                <Badge variant={proctorWarnings?.multiPerson?.additionalPeopleDetected ? 'destructive' : 'default'}>
                  {proctorWarnings?.multiPerson?.additionalPeopleDetected ? 'Detected' : 'None'}
                </Badge>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="violations" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Tab Switches</span>
                <span className="font-bold text-red-600">{proctorWarnings?.tabSwitch || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Fullscreen Exits</span>
                <span className="font-bold text-red-600">{proctorWarnings?.fullScreen || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Item Detections</span>
                <span className="font-bold text-orange-600">{proctorWarnings?.objectDetection?.itemDetectionCount || 0}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Multi-Person Violations</span>
                <span className="font-bold text-red-600">{proctorWarnings?.multiPerson?.violations || 0}</span>
              </div>
            </div>

            {riskAssessment.level !== 'LOW' && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <Icon name="AlertTriangle" className="h-4 w-4" />
                  <span className="font-medium">Risk Assessment</span>
                </div>
                <p className="mt-1 text-sm text-red-600">
                  This interview has been flagged as {riskAssessment.level} risk (Score: {riskAssessment.score}). 
                  {riskAssessment.level === 'CRITICAL' && ' Immediate review recommended.'}
                  {riskAssessment.level === 'HIGH' && ' Manual review suggested.'}
                  {riskAssessment.level === 'MEDIUM' && ' Monitor for additional violations.'}
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
