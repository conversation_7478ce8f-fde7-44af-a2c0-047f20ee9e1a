'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoomAudioRenderer,
  TrackReference,
  VideoTrack,
  useTracks,
} from '@livekit/components-react';
import { Track } from 'livekit-client';

export default function LiveKitVideoInterview({ token, url }: { token: string; url: string }) {
  return (
    <LiveKitRoom
      token={token}
      serverUrl={url}
      connect
      video
      audio
      style={{ height: '100vh', width: '100%' }}
    >
      <RoomAudioRenderer />
      <VideoGallery />
    </LiveKitRoom>
  );
}

function VideoGallery() {
  const tracks = useTracks([{ source: Track.Source.Camera, withPlaceholder: false }]);

  return (
    <div
      style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '12px',
        padding: '1rem',
      }}
    >
      {tracks
        .filter((trackRef): trackRef is TrackReference => !!trackRef.publication)
        .map((trackRef) => (
          <VideoTrack
            key={trackRef.publication!.trackSid}
            trackRef={trackRef}
            style={{
              width: '300px',
              height: '200px',
              borderRadius: '8px',
              backgroundColor: '#000',
            }}
          />
        ))}
    </div>
  );
}
