'use client';

import React, { useState } from 'react';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Input } from '@camped-ui/input';
import { Label } from '@camped-ui/label';

import OnboardingTemplate from '../onboarding-stepper/onboarding-template';
import { createUserProfile } from '@/services/apicall';

interface CandidateNameFormProps {
  onComplete: (data: { name: string }) => void;
  session: any;
  initialName?: string;
}

const CandidateNameForm = ({ onComplete, session, initialName }: CandidateNameFormProps) => {
  const [name, setName] = useState(initialName || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      toast.error('Please enter your full name');
      return;
    }

    if (name.trim().length < 2) {
      toast.error('Please enter a valid name');
      return;
    }

    setIsLoading(true);

    try {
      // Create or update user profile with the name
      const profileData = {
        fullName: name.trim(),
        userId: session?.userId,
      };

      const response = await createUserProfile(profileData);

      if (response) {
        toast.success('Name saved successfully');
        onComplete({ name: name.trim() });
      } else {
        throw new Error('Failed to save profile');
      }
    } catch (error) {
      console.error('Error saving name:', error);
      toast.error('Failed to save name. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-full w-full flex-1 flex-col items-center gap-8 p-6 overflow-y-auto">
      {/* Progress indicator */}
      <div className="flex items-center gap-2 text-xs">
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Welcome</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
            2
          </div>
          <span className="text-xs font-medium text-blue-600">Personal Info</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            3
          </div>
          <span className="text-xs text-gray-500">ID Verification</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            4
          </div>
          <span className="text-xs text-gray-500">Face Verification</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            5
          </div>
          <span className="text-xs text-gray-500">Guidelines</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            6
          </div>
          <span className="text-xs text-gray-500">Setup</span>
        </div>
      </div>

      <OnboardingTemplate
        title="Welcome to Your Interview"
        description={initialName
          ? "Please confirm your full name as it appears on your government ID. This helps us verify your identity for the interview process."
          : "Before we begin, please provide your full name as it appears on your government ID. This helps us verify your identity for the interview process."
        }
      />

      <div className="w-full max-w-md">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="fullName" className="text-sm font-medium">
              Full Name *
            </Label>
            <Input
              id="fullName"
              type="text"
              placeholder="Enter your full name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full"
              required
              disabled={isLoading}
              autoFocus
            />
            <p className="text-xs text-muted-foreground">
              Please enter your name exactly as it appears on your government-issued ID
            </p>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || !name.trim()}
          >
            {isLoading ? 'Saving...' : 'Continue to ID Verification'}
          </Button>
        </form>
      </div>

      <div className="text-center text-xs text-muted-foreground max-w-md">
        <p>
          Your information is secure and will only be used for interview verification purposes.
          We comply with all data protection regulations.
        </p>
      </div>
    </div>
  );
};

export default CandidateNameForm;
