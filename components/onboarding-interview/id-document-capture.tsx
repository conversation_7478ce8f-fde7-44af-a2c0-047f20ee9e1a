'use client';

import React, { useRef, useState, useCallback } from 'react';
import Webcam from 'react-webcam';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Icon } from '@/icons';

import OnboardingTemplate from '../onboarding-stepper/onboarding-template';

interface IdDocumentCaptureProps {
  onComplete: (data: {
    idDocument: string;
    idDocumentUrl?: string;
    validationResult?: {
      isValid: boolean;
      reason: string;
      details?: any;
    };
  }) => void;
  candidateName: string;
  userId: string;
  interviewId: string;
}

const IdDocumentCapture = ({ onComplete, candidateName, userId, interviewId }: IdDocumentCaptureProps) => {
  const webcamRef = useRef<Webcam | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [cameraLoaded, setCameraLoaded] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    reason: string;
    details?: any;
  } | null>(null);
  const [showValidationError, setShowValidationError] = useState(false);

  const videoConstraints = {
    width: 1280,
    height: 720,
    facingMode: 'user',
  };

  const handleUserMedia = useCallback(() => {
    setTimeout(() => {
      setCameraLoaded(true);
      setCameraError(false);
    }, 1000);
  }, []);

  const handleUserMediaError = useCallback((error: any) => {
    console.error('Camera error:', error);
    setCameraError(true);
    setCameraLoaded(false);
    toast.error('Camera access denied. Please allow camera access to continue.');
  }, []);

  const capturePhoto = useCallback(async () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        setCapturedImage(imageSrc);
        setValidationResult(null);
        setShowValidationError(false);

        // Automatically validate the captured document
        await validateDocument(imageSrc);
      }
    }
  }, [userId, interviewId]);

  const validateDocument = async (imageData: string) => {
    setIsValidating(true);

    try {
      const response = await fetch('/api/candidate/validate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData,
          userId,
          interviewId,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setValidationResult(result.validation);
        if (!result.validation.isValid) {
          setShowValidationError(true);
          toast.error(result.validation.reason);
        } else {
          toast.success('Document validated successfully!');
        }
      } else {
        setValidationResult({
          isValid: false,
          reason: result.validation?.reason || 'Failed to validate document',
        });
        setShowValidationError(true);
        toast.error('Document validation failed. Please try again.');
      }
    } catch (error) {
      console.error('Document validation error:', error);
      setValidationResult({
        isValid: false,
        reason: 'Technical error during validation',
      });
      setShowValidationError(true);
      toast.error('Unable to validate document. Please try again.');
    } finally {
      setIsValidating(false);
    }
  };

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    setValidationResult(null);
    setShowValidationError(false);
  }, []);

  const handleContinue = async () => {
    if (!capturedImage) {
      toast.error('Please capture your ID document first');
      return;
    }

    // Check if document validation is required and passed
    if (validationResult && !validationResult.isValid) {
      toast.error('Please capture a valid ID document before continuing');
      return;
    }

    // If no validation result yet, validate first
    if (!validationResult) {
      toast.error('Please wait for document validation to complete');
      return;
    }

    setIsProcessing(true);

    try {
      // Upload ID document to GCP storage
      const uploadResponse = await fetch('/api/candidate/upload-identity-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: capturedImage,
          documentType: 'id-document',
          userId: userId,
          interviewId: interviewId,
        }),
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload ID document');
      }

      const uploadResult = await uploadResponse.json();

      // Pass both the base64 image data, uploaded file URL, and validation result
      onComplete({
        idDocument: capturedImage,
        idDocumentUrl: uploadResult.fileUrl,
        validationResult,
      });

      toast.success('ID document captured and saved successfully');
    } catch (error) {
      console.error('Error processing ID document:', error);
      toast.error('Failed to process ID document. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex min-h-full w-full flex-1 flex-col items-center gap-8 p-6 overflow-y-auto">
      {/* Progress indicator */}
      <div className="flex items-center gap-2 text-xs">
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Welcome</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Personal Info</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
            3
          </div>
          <span className="text-xs font-medium text-blue-600">ID Verification</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            4
          </div>
          <span className="text-xs text-gray-500">Face Verification</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            5
          </div>
          <span className="text-xs text-gray-500">Guidelines</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            6
          </div>
          <span className="text-xs text-gray-500">Setup</span>
        </div>
      </div>

      <OnboardingTemplate
        title={`Hi ${candidateName}!`}
        description="Please show your government-issued ID (Driver's License, Passport, or National ID) to the camera. Make sure the document is clearly visible and readable."
      />

      <div className="w-full max-w-2xl">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="relative mb-6">
            <div className="aspect-video w-full overflow-hidden rounded-lg bg-gray-100">
              {capturedImage ? (
                <img 
                  src={capturedImage} 
                  alt="Captured ID Document" 
                  className="h-full w-full object-cover" 
                />
              ) : (
                <div className="relative h-full w-full">
                  {cameraError ? (
                    <div className="flex h-full flex-col items-center justify-center text-center">
                      <Icon name="AlertTriangle" className="mb-2 h-8 w-8 text-amber-500" />
                      <p className="text-sm text-muted-foreground">
                        Camera access denied or unavailable
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Please allow camera access and refresh the page
                      </p>
                    </div>
                  ) : (
                    <>
                      <Webcam
                        ref={webcamRef}
                        audio={false}
                        screenshotFormat="image/jpeg"
                        videoConstraints={videoConstraints}
                        onUserMedia={handleUserMedia}
                        onUserMediaError={handleUserMediaError}
                        className="h-full w-full object-cover"
                      />
                      {!cameraLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className="text-sm text-muted-foreground">Loading camera...</p>
                          </div>
                        </div>
                      )}
                      {/* ID Document overlay guide */}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="border-2 border-white border-dashed rounded-lg w-3/4 h-1/2 flex items-center justify-center">
                          <span className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
                            Position ID Document Here
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Validation Status */}
          {capturedImage && (
            <div className="mb-4">
              {isValidating ? (
                <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm text-blue-700">Validating document...</span>
                </div>
              ) : validationResult ? (
                <div className={`flex items-center gap-2 p-3 rounded-lg ${
                  validationResult.isValid
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <Icon
                    name={validationResult.isValid ? "CheckCircle" : "XCircle"}
                    className={`h-4 w-4 ${
                      validationResult.isValid ? 'text-green-600' : 'text-red-600'
                    }`}
                  />
                  <div className="flex-1">
                    <span className={`text-sm font-medium ${
                      validationResult.isValid ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {validationResult.isValid ? 'Document Validated' : 'Validation Failed'}
                    </span>
                    <p className={`text-xs mt-1 ${
                      validationResult.isValid ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {validationResult.reason}
                    </p>
                   
                  </div>
                </div>
              ) : null}
            </div>
          )}

          <div className="flex gap-3">
            {!capturedImage ? (
              <Button
                onClick={capturePhoto}
                disabled={!cameraLoaded || cameraError}
                className="flex-1"
              >
                <Icon name="Camera" className="mr-2 h-4 w-4" />
                Capture ID Document
              </Button>
            ) : (
              <>
                <Button
                  onClick={retakePhoto}
                  variant="outline"
                  className="flex-1"
                >
                  <Icon name="RotateCcw" className="mr-2 h-4 w-4" />
                  Retake Photo
                </Button>
                <Button
                  onClick={handleContinue}
                  disabled={isProcessing || isValidating || !validationResult?.isValid}
                  className="flex-1"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : isValidating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Validating...
                    </>
                  ) : validationResult?.isValid ? (
                    <>
                      <Icon name="Check" className="mr-2 h-4 w-4" />
                      Continue to Face Verification
                    </>
                  ) : (
                    <>
                      <Icon name="AlertTriangle" className="mr-2 h-4 w-4" />
                      Document Invalid
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="text-center text-xs text-muted-foreground max-w-md">
        <p>
          <strong>Accepted Documents:</strong> Driver's License, Passport, National ID Card, or other government-issued photo ID.
          Make sure the document is not expired and the photo is clearly visible.
        </p>
      </div>
    </div>
  );
};

export default IdDocumentCapture;
