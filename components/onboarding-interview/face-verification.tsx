'use client';

import React, { useRef, useState, useCallback } from 'react';
import Webcam from 'react-webcam';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Icon } from '@/icons';

import OnboardingTemplate from '../onboarding-stepper/onboarding-template';

interface FaceVerificationProps {
  onComplete: (data: {
    facePhoto: string;
    facePhotoUrl?: string;
    verificationStatus: string;
  }) => void;
  candidateName: string;
  userId: string;
  interviewId: string;
  idDocument: string;
}

const FaceVerification = ({ onComplete, candidateName, userId, interviewId, idDocument }: FaceVerificationProps) => {
  const webcamRef = useRef<Webcam | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [cameraLoaded, setCameraLoaded] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationResult, setVerificationResult] = useState<{
    success: boolean;
    confidence: number;
    message: string;
  } | null>(null);

  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: 'user',
  };

  const handleUserMedia = useCallback(() => {
    setTimeout(() => {
      setCameraLoaded(true);
      setCameraError(false);
    }, 1000);
  }, []);

  const handleUserMediaError = useCallback((error: any) => {
    console.error('Camera error:', error);
    setCameraError(true);
    setCameraLoaded(false);
    toast.error('Camera access denied. Please allow camera access to continue.');
  }, []);

  const capturePhoto = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        setCapturedImage(imageSrc);
      }
    }
  }, []);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    setVerificationResult(null);
  }, []);

  const performFaceVerification = async () => {
    if (!capturedImage || !idDocument) {
      toast.error('Missing required images for verification');
      return;
    }

    setIsVerifying(true);

    try {
      // Call our face verification API
      const response = await fetch('/api/candidate/verify-identity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          facePhoto: capturedImage,
          idDocument: idDocument,
          candidateName: candidateName,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setVerificationResult({
          success: result.success,
          confidence: result.confidence || 0,
          message: result.message || 'Verification completed',
        });

        // Save face verification results to database
        try {
          await fetch('/api/candidate/save-face-verification', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: userId,
              interviewId: interviewId,
              verificationScore: result.confidence,
              verificationStatus: result.success ? 'verified' : 'failed',
              faceMatches: result.faceMatches || 0,
              unmatched: result.unmatched || 0,
              verificationMessage: result.message,
            }),
          });
        } catch (saveError) {
          console.error('Error saving verification results:', saveError);
          // Don't fail the verification if saving fails
        }

        if (result.success) {
          toast.success('Face verification successful!');
        } else {
          toast.error('Face verification failed. Please try again.');
        }
      } else {
        throw new Error(result.error || 'Verification failed');
      }
    } catch (error) {
      console.error('Face verification error:', error);
      toast.error('Verification failed. Please try again.');
      setVerificationResult({
        success: false,
        confidence: 0,
        message: 'Verification failed due to technical error',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleContinue = async () => {
    if (!verificationResult?.success) {
      toast.error('Please complete face verification successfully before continuing');
      return;
    }

    try {
      // Convert base64 to blob for profile image upload
      const base64Data = capturedImage!.replace(/^data:image\/[a-z]+;base64,/, '');
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/jpeg' });

      // Get presigned URL for profile image upload
      const { updateProfileImage, createUserProfile } = await import('@/services/apicall');

      const res = await updateProfileImage(userId, 'user-profile', 'image/jpeg');
      const { url } = res;

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: 'PUT',
        body: blob,
        headers: {
          'Content-Type': 'image/jpeg',
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload profile image');
      }

      // Update user profile
      await createUserProfile({
        userId: userId,
        updatedAt: new Date(),
      });

      onComplete({
        facePhoto: capturedImage!,
        facePhotoUrl: url,
        verificationStatus: 'verified',
      });

     
    } catch (error) {
      console.error('Error saving face photo:', error);
      toast.error('Failed to save face photo. Please try again.');
    }
  };

  return (
    <div className="flex min-h-full w-full flex-1 flex-col items-center gap-8 p-6 overflow-y-auto">
      {/* Progress indicator */}
      <div className="flex items-center gap-2 text-xs">
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Welcome</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">Personal Info</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-600 text-xs font-medium text-white">
            ✓
          </div>
          <span className="text-xs text-green-600">ID Verification</span>
        </div>
        <div className="h-px w-6 bg-green-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
            4
          </div>
          <span className="text-xs font-medium text-blue-600">Face Verification</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            5
          </div>
          <span className="text-xs text-gray-500">Guidelines</span>
        </div>
        <div className="h-px w-6 bg-gray-300"></div>
        <div className="flex items-center gap-1">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-medium text-gray-500">
            6
          </div>
          <span className="text-xs text-gray-500">Setup</span>
        </div>
      </div>

      <OnboardingTemplate
        title="Face Verification"
        description="Please look directly at the camera and capture a clear photo of your face. We'll compare this with your ID document to verify your identity."
      />

      <div className="w-full max-w-2xl">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <div className="relative mb-6">
            <div className="aspect-[4/3] w-full overflow-hidden rounded-lg bg-gray-100">
              {capturedImage ? (
                <img 
                  src={capturedImage} 
                  alt="Captured Face Photo" 
                  className="h-full w-full object-cover" 
                />
              ) : (
                <div className="relative h-full w-full">
                  {cameraError ? (
                    <div className="flex h-full flex-col items-center justify-center text-center">
                      <Icon name="AlertTriangle" className="mb-2 h-8 w-8 text-amber-500" />
                      <p className="text-sm text-muted-foreground">
                        Camera access denied or unavailable
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Please allow camera access and refresh the page
                      </p>
                    </div>
                  ) : (
                    <>
                      <Webcam
                        ref={webcamRef}
                        audio={false}
                        screenshotFormat="image/jpeg"
                        videoConstraints={videoConstraints}
                        onUserMedia={handleUserMedia}
                        onUserMediaError={handleUserMediaError}
                        className="h-full w-full object-cover"
                      />
                      {!cameraLoaded && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className="text-sm text-muted-foreground">Loading camera...</p>
                          </div>
                        </div>
                      )}
                      {/* Face detection overlay guide */}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="border-2 border-white border-dashed rounded-full w-48 h-48 flex items-center justify-center">
                          <span className="bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
                            Position Face Here
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Verification Result */}
          {verificationResult && (
            <div className={`mb-4 p-4 rounded-lg ${
              verificationResult.success 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex items-center gap-2">
                <Icon 
                  name={verificationResult.success ? "CheckCircle" : "XCircle"} 
                  className={`h-5 w-5 ${
                    verificationResult.success ? 'text-green-600' : 'text-red-600'
                  }`} 
                />
                <span className={`font-medium ${
                  verificationResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {verificationResult.success ? 'Verification Successful' : 'Verification Failed'}
                </span>
              </div>
              <p className={`text-sm mt-1 ${
                verificationResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {verificationResult.message}
              </p>
              {verificationResult.confidence > 0 && (
                <p className={`text-xs mt-1 ${
                  verificationResult.success ? 'text-green-600' : 'text-red-600'
                }`}>
                  Confidence: {Math.round(verificationResult.confidence)}%
                </p>
              )}
            </div>
          )}

          <div className="flex gap-3">
            {!capturedImage ? (
              <Button
                onClick={capturePhoto}
                disabled={!cameraLoaded || cameraError}
                className="flex-1"
              >
                <Icon name="Camera" className="mr-2 h-4 w-4" />
                Capture Face Photo
              </Button>
            ) : !verificationResult ? (
              <>
                <Button
                  onClick={retakePhoto}
                  variant="outline"
                  className="flex-1"
                >
                  <Icon name="RotateCcw" className="mr-2 h-4 w-4" />
                  Retake Photo
                </Button>
                <Button
                  onClick={performFaceVerification}
                  disabled={isVerifying}
                  className="flex-1"
                >
                  {isVerifying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      <Icon name="Shield" className="mr-2 h-4 w-4" />
                      Verify Identity
                    </>
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button
                  onClick={retakePhoto}
                  variant="outline"
                  className="flex-1"
                >
                  <Icon name="RotateCcw" className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
                <Button
                  onClick={handleContinue}
                  disabled={!verificationResult.success}
                  className="flex-1"
                >
                  <Icon name="ArrowRight" className="mr-2 h-4 w-4" />
                  Continue to Guidelines
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="text-center text-xs text-muted-foreground max-w-md">
        <p>
          <strong>Face Verification:</strong> We use advanced facial recognition technology to compare your live photo with your ID document.
          This ensures the security and integrity of the interview process.
        </p>
      </div>
    </div>
  );
};

export default FaceVerification;
