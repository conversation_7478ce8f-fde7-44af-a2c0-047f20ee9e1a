'use client';

import {
  <PERSON>ert<PERSON>ircle,
  Award,
  Brain,
  CheckCircle,
  MessageSquare,
  Star,
  Target,
  ThumbsDown,
  ThumbsUp,
  TrendingUp,
  Users,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Progress } from '@camped-ui/progress';

interface ComprehensiveFeedbackDisplayProps {
  feedback: any;
  candidateName?: string;
}

export const ComprehensiveFeedbackDisplay = ({
  feedback,
  candidateName = 'Candidate',
}: ComprehensiveFeedbackDisplayProps) => {
  // Dynamic color scheme based on content/status
  const getStatusColors = (type: string, value?: any) => {
    switch (type) {
      case 'recommendation':
        const decision = value?.toLowerCase();
        if (decision?.includes('recommend') && !decision?.includes('not')) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (decision?.includes('consider')) {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }

      case 'proctoring':
        const riskLevel = value?.toLowerCase();
        if (riskLevel === 'low' || riskLevel === 'minimal') {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (riskLevel === 'medium') {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }

      case 'strengths':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/50',
          text: 'text-green-600',
          icon: 'text-green-600',
        };

      case 'improvements':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/50',
          text: 'text-red-600',
          icon: 'text-red-600',
        };

      case 'cultural_fit':
        const culturalFit = value?.toLowerCase();
        if (culturalFit?.includes('fit') && !culturalFit?.includes('unfit') && !culturalFit?.includes('poor')) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (culturalFit?.includes('unfit') || culturalFit?.includes('poor') || culturalFit?.includes('bad')) {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        } else {
          // For scores, use score-based logic
          const score = typeof value === 'number' ? value : 0;
          if (score >= 80) {
            return {
              border: 'border-green-200',
              bg: 'bg-green-50/50',
              text: 'text-green-600',
              icon: 'text-green-600',
            };
          } else if (score >= 60) {
            return {
              border: 'border-yellow-200',
              bg: 'bg-yellow-50/50',
              text: 'text-yellow-600',
              icon: 'text-yellow-600',
            };
          } else {
            return {
              border: 'border-red-200',
              bg: 'bg-red-50/50',
              text: 'text-red-600',
              icon: 'text-red-600',
            };
          }
        }

      case 'job_fit_score':
        const score = typeof value === 'number' ? value : 0;
        if (score >= 80) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (score >= 60) {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }

      default:
        return {
          border: 'border-slate-200',
          bg: 'bg-slate-50/50',
          text: 'text-slate-600',
          icon: 'text-slate-600',
        };
    }
  };



  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getRecommendationIcon = (decision: string) => {
    switch (decision?.toLowerCase()) {
      case 'strongly recommend':
      case 'strongly_recommend':
        return <ThumbsUp className="h-5 w-5 text-green-600" />;
      case 'recommend':
        return <ThumbsUp className="h-5 w-5 text-green-500" />;
      case 'consider':
        return <MessageSquare className="h-5 w-5 text-yellow-500" />;
      case 'do not recommend':
      case 'do_not_recommend':
        return <ThumbsDown className="h-5 w-5 text-red-500" />;
      case 'strongly do not recommend':
      case 'strongly_do_not_recommend':
        return <ThumbsDown className="h-5 w-5 text-red-600" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-500" />;
    }
  };

  const coreMetrics = [
    { key: 'communication', label: 'Communication', icon: MessageSquare },
    { key: 'confidence', label: 'Confidence', icon: TrendingUp },
    { key: 'clarity', label: 'Clarity', icon: Target },
    { key: 'passion', label: 'Passion', icon: Star },
    { key: 'professionalism', label: 'Professionalism', icon: Users },
    { key: 'role_specific_competency', label: 'Role Competency', icon: Brain },
    { key: 'cultural_fit', label: 'Cultural Fit', icon: Users },
  ];

  return (
    <div className="mx-auto w-full max-w-6xl space-y-6">
      {/* Combined Recommendation and Assessment */}
      {feedback?.hiring_recommendation && (
        <Card className={`border-2 ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).border} ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).bg}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getRecommendationIcon(feedback.hiring_recommendation.decision)}
                <div>
                  <CardTitle className="text-xl">Interview Assessment Summary</CardTitle>
                  <CardDescription>
                    Comprehensive evaluation for {candidateName}
                  </CardDescription>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getStatusColors('job_fit_score', feedback?.overall_score).text}`}>
                  {Math.round(feedback?.overall_score || 0)}/100
                </div>
                <div className="text-sm text-gray-600">Overall Score</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Summary Metrics Row */}
              <div className="grid grid-cols-3 gap-4">
                {/* Job Fit Score */}
                {(() => {
                  const jobFitScore = feedback?.job_fit_analysis?.overall_job_fit?.score ||
                                    feedback?.job_fit_analysis?.job_fit_score?.score ||
                                    feedback?.overall_score || 0;
                  const validScore = Math.max(0, Math.min(100, Math.round(jobFitScore)));

                  return (
                    <div className="rounded-lg border bg-white p-4 text-center">
                      <div className={`text-lg font-semibold ${getStatusColors('job_fit_score', validScore).text}`}>
                        {validScore}/100
                      </div>
                      <div className="text-sm text-gray-600">Job Fit</div>
                      <Progress
                        value={validScore}
                        className="mt-2 h-2"
                      />
                    </div>
                  );
                })()}

                {/* Hiring Recommendation */}
                <div className="rounded-lg border bg-white p-4 text-center">
                  <div className="mb-2 flex items-center justify-center gap-2">
                    {getRecommendationIcon(feedback.hiring_recommendation.decision)}
                    <span className="font-semibold">
                      {feedback.hiring_recommendation.decision
                        ?.replace(/_/g, ' ')
                        .replace(/\b\w/g, (l: string) => l.toUpperCase())}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">Recommendation</div>
                </div>

                {/* Interview Performance */}
                {(() => {
                  const performanceScore = feedback?.interview_performance?.overall_score ||
                                         feedback?.overall_score || 0;
                  const validScore = Math.max(0, Math.min(100, Math.round(performanceScore)));

                  return (
                    <div className="rounded-lg border bg-white p-4 text-center">
                      <div className={`text-lg font-semibold ${getStatusColors('job_fit_score', validScore).text}`}>
                        {validScore}/100
                      </div>
                      <div className="text-sm text-gray-600">Interview Performance</div>
                      <Progress
                        value={validScore}
                        className="mt-2 h-2"
                      />
                    </div>
                  );
                })()}
              </div>

              <div className="flex items-center gap-3">
                <span className="font-semibold">Decision:</span>
                <Badge
                  variant={
                    feedback.hiring_recommendation.decision?.includes('recommend') &&
                    !feedback.hiring_recommendation.decision?.includes('not')
                      ? 'default'
                      : 'destructive'
                  }
                  className="text-sm"
                >
                  {feedback.hiring_recommendation.decision
                    ?.replace(/_/g, ' ')
                    .replace(/\b\w/g, (l: string) => l.toUpperCase())}
                </Badge>
              </div>

              {feedback.hiring_recommendation.key_deciding_factors && (
                <div>
                  <h4 className="mb-2 font-semibold">Key Deciding Factors:</h4>
                  <ul className="space-y-1">
                    {feedback.hiring_recommendation.key_deciding_factors.map(
                      (factor: string, index: number) => (
                        <li key={index} className="flex items-start gap-2">
                          <Target className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).icon}`} />
                          <span className="text-sm">{factor}</span>
                        </li>
                      ),
                    )}
                  </ul>
                </div>
              )}

              <div>
                <h4 className="mb-2 font-semibold">Reasoning:</h4>
                <p className="leading-relaxed text-gray-700">
                  {feedback.hiring_recommendation.reasoning}
                </p>
              </div>

              {/* Short Summary */}
              {feedback?.short_summary && (
                <div className="mt-4 rounded-lg border bg-white p-4">
                  <h4 className="mb-2 font-semibold">Executive Summary</h4>
                  <p className="leading-relaxed text-gray-700">{feedback.short_summary}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Job Fit Analysis - Enhanced */}
      {feedback?.job_fit_analysis && (
        <Card className={`border-2 ${getStatusColors('job_fit_score', feedback.job_fit_analysis.overall_job_fit?.score).border} ${getStatusColors('job_fit_score', feedback.job_fit_analysis.overall_job_fit?.score).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('job_fit_score', feedback.job_fit_analysis.overall_job_fit?.score).text}`}>
              <Users className={`h-5 w-5 ${getStatusColors('job_fit_score', feedback.job_fit_analysis.overall_job_fit?.score).icon}`} />
              Job Fit Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Main Job Fit Metrics */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {Object.entries(feedback.job_fit_analysis)
                  .filter(([key]) => !['red_flags', 'strong_indicators'].includes(key))
                  .map(([key, value]: [string, any]) => (
                    <div key={key} className="rounded-lg border p-4">
                      <div className="mb-2 flex items-center justify-between">
                        <span className="font-medium">
                          {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </span>
                        <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                      </div>
                      <Progress value={value.score} className="mb-3 h-3" />
                      <p className="text-sm text-gray-700">{value.feedback}</p>
                    </div>
                  ))}
              </div>

              {/* Red Flags and Strong Indicators */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {feedback.job_fit_analysis.red_flags &&
                  feedback.job_fit_analysis.red_flags.length > 0 && (
                    <div className="rounded-lg border-2 border-red-200 bg-red-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-red-700">
                        <AlertCircle className="h-4 w-4" />
                        Red Flags
                      </h4>
                      <ul className="space-y-1">
                        {feedback.job_fit_analysis.red_flags.map((flag: string, index: number) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-red-600">
                            <span className="mt-1 text-red-500">•</span>
                            {flag}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {feedback.job_fit_analysis.strong_indicators &&
                  feedback.job_fit_analysis.strong_indicators.length > 0 && (
                    <div className="rounded-lg border-2 border-green-200 bg-green-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-green-700">
                        <CheckCircle className="h-4 w-4" />
                        Strong Indicators
                      </h4>
                      <ul className="space-y-1">
                        {feedback.job_fit_analysis.strong_indicators.map(
                          (indicator: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-green-600"
                            >
                              <span className="mt-1 text-green-500">•</span>
                              {indicator}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Skills Analysis */}
      <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
        <CardHeader>
          <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
            <Target className={`h-5 w-5 ${getStatusColors('default').icon}`} />
            Detailed Skills Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {coreMetrics.map((metric) => {
              const data = feedback?.[metric.key];
              if (!data || !data.feedback) return null;

              const Icon = metric.icon;
              return (
                <div key={metric.key} className="rounded-lg border p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{metric.label}</span>
                    </div>
                    <Badge variant={getScoreBadgeVariant(data.score)}>{data.score}/100</Badge>
                  </div>
                  <Progress value={data.score} className="mb-3 h-3" />
                  <p className="text-sm leading-relaxed text-gray-700">{data.feedback}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Strengths and Improvements */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {feedback?.strengths && (
          <Card className={`border-2 ${getStatusColors('strengths').border} ${getStatusColors('strengths').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('strengths').text}`}>
                <CheckCircle className={`h-5 w-5 ${getStatusColors('strengths').icon}`} />
                Key Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {feedback.strengths.map((strength: string, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <Star className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('strengths').icon}`} />
                    <span className="text-sm">{strength}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {feedback?.areas_of_improvement && (
          <Card className={`border-2 ${getStatusColors('improvements').border} ${getStatusColors('improvements').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('improvements').text}`}>
                <AlertCircle className={`h-5 w-5 ${getStatusColors('improvements').icon}`} />
                Areas for Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {feedback.areas_of_improvement.map((area: string, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <TrendingUp className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('improvements').icon}`} />
                    <span className="text-sm">{area}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Comprehensive Assessment Sections */}
      {feedback?.behavioral_competencies && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <Users className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Behavioral Competencies
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {Object.entries(feedback.behavioral_competencies).map(
                ([key, value]: [string, any]) => (
                  <div key={key} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                      <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                    </div>
                    <Progress value={value.score} className="mb-3 h-3" />
                    <p className="text-sm text-gray-700">{value.feedback}</p>
                  </div>
                ),
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {feedback?.business_acumen && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <TrendingUp className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Business Acumen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {Object.entries(feedback.business_acumen).map(([key, value]: [string, any]) => (
                <div key={key} className="rounded-lg border p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <span className="font-medium">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                    </span>
                    <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                  </div>
                  <Progress value={value.score} className="mb-3 h-3" />
                  <p className="text-sm text-gray-700">{value.feedback}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {feedback?.growth_potential && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <Star className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Growth Potential
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {Object.entries(feedback.growth_potential).map(([key, value]: [string, any]) => (
                <div key={key} className="rounded-lg border p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <span className="font-medium">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                    </span>
                    <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                  </div>
                  <Progress value={value.score} className="mb-3 h-3" />
                  <p className="text-sm text-gray-700">{value.feedback}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Assessment */}
      {feedback?.comprehensive_risk_assessment && (
        <Card className={`border-2 ${getStatusColors('proctoring', feedback.comprehensive_risk_assessment.overall_risk_level?.level).border} ${getStatusColors('proctoring', feedback.comprehensive_risk_assessment.overall_risk_level?.level).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('proctoring', feedback.comprehensive_risk_assessment.overall_risk_level?.level).text}`}>
              <AlertCircle className={`h-5 w-5 ${getStatusColors('proctoring', feedback.comprehensive_risk_assessment.overall_risk_level?.level).icon}`} />
              Comprehensive Risk Assessment
            </CardTitle>
            <CardDescription>
              Critical risk factors to consider before making an offer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {feedback.comprehensive_risk_assessment.overall_risk_level && (
                <div className="rounded-lg border-2 border-yellow-300 bg-yellow-100 p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <span className="font-semibold">Overall Risk Level:</span>
                    <Badge
                      variant={
                        feedback.comprehensive_risk_assessment.overall_risk_level.level === 'Low'
                          ? 'default'
                          : feedback.comprehensive_risk_assessment.overall_risk_level.level ===
                            'Medium'
                          ? 'secondary'
                          : 'destructive'
                      }
                    >
                      {feedback.comprehensive_risk_assessment.overall_risk_level.level}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700">
                    {feedback.comprehensive_risk_assessment.overall_risk_level.reasoning}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {feedback.comprehensive_risk_assessment.performance_risks &&
                  feedback.comprehensive_risk_assessment.performance_risks.length > 0 && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                      <h4 className="mb-2 font-semibold text-red-700">Performance Risks</h4>
                      <ul className="space-y-1">
                        {feedback.comprehensive_risk_assessment.performance_risks.map(
                          (risk: string, index: number) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-red-600">
                              <span className="mt-1 text-red-500">•</span>
                              {risk}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                {feedback.comprehensive_risk_assessment.cultural_risks &&
                  feedback.comprehensive_risk_assessment.cultural_risks.length > 0 && (
                    <div className="rounded-lg border border-orange-200 bg-orange-50 p-4">
                      <h4 className="mb-2 font-semibold text-orange-700">Cultural Risks</h4>
                      <ul className="space-y-1">
                        {feedback.comprehensive_risk_assessment.cultural_risks.map(
                          (risk: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-orange-600"
                            >
                              <span className="mt-1 text-orange-500">•</span>
                              {risk}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                {feedback.comprehensive_risk_assessment.retention_risks &&
                  feedback.comprehensive_risk_assessment.retention_risks.length > 0 && (
                    <div className="rounded-lg border border-purple-200 bg-purple-50 p-4">
                      <h4 className="mb-2 font-semibold text-purple-700">Retention Risks</h4>
                      <ul className="space-y-1">
                        {feedback.comprehensive_risk_assessment.retention_risks.map(
                          (risk: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-purple-600"
                            >
                              <span className="mt-1 text-purple-500">•</span>
                              {risk}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                {feedback.comprehensive_risk_assessment.integrity_concerns &&
                  feedback.comprehensive_risk_assessment.integrity_concerns.length > 0 && (
                    <div className="rounded-lg border border-red-300 bg-red-100 p-4">
                      <h4 className="mb-2 font-semibold text-red-800">Integrity Concerns</h4>
                      <ul className="space-y-1">
                        {feedback.comprehensive_risk_assessment.integrity_concerns.map(
                          (concern: string, index: number) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-red-700">
                              <span className="mt-1 text-red-600">•</span>
                              {concern}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interviewer Performance Review */}
      {feedback?.interviewer_performance_review && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <Brain className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Interviewer Performance Review
            </CardTitle>
            <CardDescription>Coaching feedback to help improve future interviews</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Performance Metrics */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {feedback.interviewer_performance_review.overall_interview_quality && (
                  <div className="rounded-lg border bg-white p-4 text-center">
                    <div className={`text-lg font-semibold ${getStatusColors('job_fit_score', feedback.interviewer_performance_review.overall_interview_quality.score).text}`}>
                      {feedback.interviewer_performance_review.overall_interview_quality.score}/100
                    </div>
                    <div className="text-sm text-gray-600">Overall Quality</div>
                    <Progress
                      value={
                        feedback.interviewer_performance_review.overall_interview_quality.score
                      }
                      className="mt-2 h-2"
                    />
                  </div>
                )}

                {feedback.interviewer_performance_review.question_effectiveness && (
                  <div className="rounded-lg border bg-white p-4 text-center">
                    <div className={`text-lg font-semibold ${getStatusColors('job_fit_score', feedback.interviewer_performance_review.question_effectiveness.score).text}`}>
                      {feedback.interviewer_performance_review.question_effectiveness.score}/100
                    </div>
                    <div className="text-sm text-gray-600">Question Quality</div>
                    <Progress
                      value={feedback.interviewer_performance_review.question_effectiveness.score}
                      className="mt-2 h-2"
                    />
                  </div>
                )}

                {feedback.interviewer_performance_review.interview_flow_and_control && (
                  <div className="rounded-lg border bg-white p-4 text-center">
                    <div className={`text-lg font-semibold ${getStatusColors('job_fit_score', feedback.interviewer_performance_review.interview_flow_and_control.score).text}`}>
                      {feedback.interviewer_performance_review.interview_flow_and_control.score}/100
                    </div>
                    <div className="text-sm text-gray-600">Flow & Control</div>
                    <Progress
                      value={
                        feedback.interviewer_performance_review.interview_flow_and_control.score
                      }
                      className="mt-2 h-2"
                    />
                  </div>
                )}
              </div>

              {/* Detailed Feedback */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Strengths and Coaching */}
                <div className="space-y-4">
                  {feedback.interviewer_performance_review.interviewer_strengths && (
                    <div className="rounded-lg border-2 border-green-200 bg-green-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-green-700">
                        <CheckCircle className="h-4 w-4" />
                        Interviewer Strengths
                      </h4>
                      <ul className="space-y-1">
                        {feedback.interviewer_performance_review.interviewer_strengths.map(
                          (strength: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-green-600"
                            >
                              <Star className="mt-1 h-3 w-3 flex-shrink-0 text-green-500" />
                              {strength}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                  {feedback.interviewer_performance_review.coaching_recommendations && (
                    <div className="rounded-lg border-2 border-blue-200 bg-blue-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-blue-700">
                        <Target className="h-4 w-4" />
                        Coaching Recommendations
                      </h4>
                      <ul className="space-y-1">
                        {feedback.interviewer_performance_review.coaching_recommendations.map(
                          (rec: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-blue-600"
                            >
                              <TrendingUp className="mt-1 h-3 w-3 flex-shrink-0 text-blue-500" />
                              {rec}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Missed Opportunities and Assessment Gaps */}
                <div className="space-y-4">
                  {feedback.interviewer_performance_review.missed_opportunities && (
                    <div className="rounded-lg border-2 border-orange-200 bg-orange-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-orange-700">
                        <AlertCircle className="h-4 w-4" />
                        Missed Opportunities
                      </h4>
                      <ul className="space-y-1">
                        {feedback.interviewer_performance_review.missed_opportunities.map(
                          (opportunity: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-orange-600"
                            >
                              <span className="mt-1 text-orange-500">•</span>
                              {opportunity}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                  {feedback.interviewer_performance_review.role_specific_assessment_gaps && (
                    <div className="rounded-lg border-2 border-red-200 bg-red-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-red-700">
                        <Target className="h-4 w-4" />
                        Role-Specific Assessment Gaps
                      </h4>
                      <ul className="space-y-1">
                        {feedback.interviewer_performance_review.role_specific_assessment_gaps.map(
                          (gap: string, index: number) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-red-600">
                              <span className="mt-1 text-red-500">•</span>
                              {gap}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}

                  {feedback.interviewer_performance_review
                    .areas_requiring_additional_assessment && (
                    <div className="rounded-lg border-2 border-blue-200 bg-blue-50 p-4">
                      <h4 className="mb-2 flex items-center gap-2 font-semibold text-blue-700">
                        <Brain className="h-4 w-4" />
                        Areas Requiring Additional Assessment
                      </h4>
                      <ul className="space-y-1">
                        {feedback.interviewer_performance_review.areas_requiring_additional_assessment.map(
                          (area: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-blue-600"
                            >
                              <span className="mt-1 text-blue-500">•</span>
                              {area}
                            </li>
                          ),
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Final Interview Completeness */}
                <div>
                  {feedback.interviewer_performance_review.final_interview_completeness && (
                    <div className="rounded-lg border-2 border-indigo-200 bg-indigo-50 p-4">
                      <div className="mb-3 flex items-center justify-between">
                        <h4 className="font-semibold text-indigo-700">
                          Final Interview Completeness
                        </h4>
                        <Badge
                          variant={getScoreBadgeVariant(
                            feedback.interviewer_performance_review.final_interview_completeness
                              .score,
                          )}
                        >
                          {
                            feedback.interviewer_performance_review.final_interview_completeness
                              .score
                          }
                          /100
                        </Badge>
                      </div>
                      <Progress
                        value={
                          feedback.interviewer_performance_review.final_interview_completeness.score
                        }
                        className="mb-3 h-3"
                      />
                      <p className="text-sm text-indigo-700">
                        {
                          feedback.interviewer_performance_review.final_interview_completeness
                            .feedback
                        }
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
