'use client';

import {
  AlertCircle,
  Award,
  Brain,
  CheckCircle,
  FileText,
  MessageSquare,
  Star,
  Target,
  ThumbsDown,
  ThumbsUp,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Progress } from '@camped-ui/progress';
import type { AssessmentCriteria, AssessmentFeedback, AssessmentResult } from '@/types/assessment';

interface DynamicAssessmentFeedbackDisplayProps {
  feedback: AssessmentFeedback | any; // Support both new and legacy formats
  assessmentCriteria?: AssessmentCriteria[];
  candidateName?: string;
  appliedRole?: string;
  appliedLevel?: string;
  isLegacyFormat?: boolean;
}

export const DynamicAssessmentFeedbackDisplay = ({
  feedback,
  assessmentCriteria = [],
  candidateName = 'Candidate',
  appliedRole = '',
  appliedLevel = '',
  isLegacyFormat = false,
}: DynamicAssessmentFeedbackDisplayProps) => {
  // Dynamic color scheme based on content/status
  const getStatusColors = (type: string, value?: any) => {
    switch (type) {
      case 'recommendation':
        const decision = value?.toLowerCase();
        if (decision?.includes('hire') && !decision?.includes('not')) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (decision?.includes('maybe')) {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }
      case 'strengths':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/50',
          text: 'text-green-600',
          icon: 'text-green-600',
        };
      case 'improvements':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/50',
          text: 'text-red-600',
          icon: 'text-red-600',
        };
      case 'proctoring':
        const riskLevel = value?.toLowerCase();
        if (riskLevel === 'low') {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (riskLevel === 'medium') {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }
      default:
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50/50',
          text: 'text-gray-700',
          icon: 'text-gray-600',
        };
    }
  };

  const getScoreBadgeVariant = (score: number | string) => {
    const numScore = typeof score === 'string' ? parseFloat(score) : score;
    if (isNaN(numScore)) {
      // For non-numeric scores (like "B1", "B2"), use secondary
      return 'secondary';
    }
    if (numScore >= 70) return 'default';  // Green for good scores
    if (numScore >= 50) return 'secondary'; // Yellow for average scores
    return 'destructive'; // Red for poor scores
  };



  const formatScaleValue = (criteria: AssessmentCriteria, value: any): string => {
    switch (criteria.scale.type) {
      case 'numeric':
        return `${value}/${criteria.scale.max || 10}`;
      case 'categorical':
        return value.toString();
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return value.toString();
    }
  };

  const getScoreProgress = (criteria: AssessmentCriteria, value: any): number => {
    switch (criteria.scale.type) {
      case 'numeric':
        const max = criteria.scale.max || 10;
        const min = criteria.scale.min || 1;
        return ((value - min) / (max - min)) * 100;
      case 'categorical':
        const labels = criteria.scale.labels || [];
        const index = labels.indexOf(value);
        return index >= 0 ? ((index + 1) / labels.length) * 100 : 0;
      case 'boolean':
        return value ? 100 : 0;
      default:
        return 0;
    }
  };

  const getCriteriaIcon = (category: string) => {
    switch (category) {
      case 'technical':
        return Brain;
      case 'communication':
        return MessageSquare;
      case 'behavioral':
        return Users;
      case 'domain_specific':
        return Target;
      default:
        return Star;
    }
  };

  // Helper functions for when criteria metadata is not available
  const getDefaultProgress = (score: any): number => {
    if (typeof score === 'number') {
      // Assume numeric scores are on a 1-10 scale, convert to percentage
      if (score <= 10) {
        return Math.min(Math.max((score / 10) * 100, 0), 100);
      }
      // If score is already a percentage (>10), use as-is
      return Math.min(Math.max(score, 0), 100);
    }
    // For categorical scores like "B1", "B2", etc., try to map to percentage
    if (typeof score === 'string') {
      const cefr = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
      const index = cefr.indexOf(score.toUpperCase());
      if (index !== -1) {
        return ((index + 1) / cefr.length) * 100;
      }
      // For other string scores, try to extract numbers
      const numMatch = score.match(/\d+/);
      if (numMatch) {
        const num = parseInt(numMatch[0]);
        // Assume extracted numbers are on 1-10 scale
        if (num <= 10) {
          return Math.min(Math.max((num / 10) * 100, 0), 100);
        }
        return Math.min(Math.max(num, 0), 100);
      }
    }
    return 50; // Default fallback
  };

  const getDefaultFormattedScore = (score: any): string => {
    if (typeof score === 'number') {
      return `${score}/10`;
    }
    return String(score);
  };

  const getRecommendationIcon = (decision: string) => {
    const lowerDecision = decision?.toLowerCase();
    if (lowerDecision?.includes('hire') && !lowerDecision?.includes('not')) {
      return <ThumbsUp className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    } else if (lowerDecision?.includes('maybe')) {
      return <AlertCircle className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    } else {
      return <ThumbsDown className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    }
  };

  // Handle legacy format fallback
  if (isLegacyFormat || !feedback?.assessment_results) {
    return (
      <div className="mx-auto w-full max-w-6xl space-y-6">
        <Card className="border-2 border-yellow-200 bg-yellow-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600">
              <AlertCircle className="h-5 w-5" />
              Legacy Assessment Format
            </CardTitle>
            <CardDescription>
              This interview was created before custom assessment criteria were available.
              Displaying standard assessment format.
            </CardDescription>
          </CardHeader>
        </Card>
        {/* Render legacy format here - could import existing component */}
      </div>
    );
  }

  return (
    <div className="mx-auto w-full max-w-6xl space-y-6">
      {/* Overall Assessment Summary */}
      {feedback?.overall_assessment && (
        <Card className={`border-2 ${getStatusColors('recommendation', feedback.overall_assessment.recommendation).border} ${getStatusColors('recommendation', feedback.overall_assessment.recommendation).bg}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getRecommendationIcon(feedback.overall_assessment.recommendation)}
                <div>
                  <CardTitle className="text-xl">Assessment Summary</CardTitle>
                  <CardDescription>
                    Overall evaluation for {candidateName} - {appliedRole} ({appliedLevel})
                  </CardDescription>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getStatusColors('recommendation', feedback.overall_assessment.recommendation).text}`}>
                  {Math.round(feedback.overall_assessment.weighted_score)}/100
                </div>
                <div className="text-sm text-gray-500">Weighted Score</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-medium">Recommendation:</span>
                <Badge variant={
                  feedback.overall_assessment.recommendation.toLowerCase().includes('hire') && 
                  !feedback.overall_assessment.recommendation.toLowerCase().includes('not') 
                    ? 'default' 
                    : feedback.overall_assessment.recommendation.toLowerCase().includes('maybe')
                    ? 'secondary'
                    : 'destructive'
                }>
                  {feedback.overall_assessment.recommendation}
                </Badge>
              </div>
              <Progress value={Math.round(feedback.overall_assessment.weighted_score)} className="h-3" />
              <p className="text-sm leading-relaxed">{feedback.overall_assessment.summary}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Custom Assessment Criteria Results */}
      {feedback?.assessment_results && Object.keys(feedback.assessment_results).length > 0 && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <Target className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Custom Assessment Criteria
            </CardTitle>
            <CardDescription>
              Detailed evaluation based on configured assessment criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(feedback.assessment_results).map(([criteriaId, result]: [string, AssessmentResult]) => {
                const criteria = assessmentCriteria.find(c => c.id === criteriaId);

                // If we have criteria metadata, use it; otherwise, infer from the data
                const criteriaName = criteria?.name || criteriaId.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
                const criteriaCategory = criteria?.category || 'custom';
                const criteriaWeight = criteria?.weight || (result as any).weight || 0;
                const criteriaDescription = criteria?.description || '';

                const Icon = getCriteriaIcon(criteriaCategory);
                const progress = criteria ? getScoreProgress(criteria, result.score) : getDefaultProgress(result.score);
                const formattedScore = criteria ? formatScaleValue(criteria, result.score) : getDefaultFormattedScore(result.score);

                return (
                  <div key={criteriaId} className="rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon className="h-5 w-5" />
                        <span className="font-medium">{criteriaName}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getScoreBadgeVariant(progress)}>{formattedScore}</Badge>
                        {criteriaWeight > 0 && (
                          <span className="text-xs text-gray-500">{criteriaWeight}%</span>
                        )}
                      </div>
                    </div>
                    <Progress value={progress} className="mb-3 h-3" />
                    {criteriaDescription && (
                      <p className="text-sm text-gray-600 mb-2">{criteriaDescription}</p>
                    )}
                    <p className="text-sm leading-relaxed text-gray-700">{result.feedback}</p>
                    {result.evidence && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-600">
                        <strong>Evidence:</strong> {result.evidence}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Strengths and Areas for Improvement */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {feedback?.overall_assessment?.strengths && (
          <Card className={`border-2 ${getStatusColors('strengths').border} ${getStatusColors('strengths').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('strengths').text}`}>
                <CheckCircle className={`h-5 w-5 ${getStatusColors('strengths').icon}`} />
                Key Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-2">
                <Star className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('strengths').icon}`} />
                <p className="text-sm leading-relaxed">{feedback.overall_assessment.strengths}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {feedback?.overall_assessment?.areas_for_improvement && (
          <Card className={`border-2 ${getStatusColors('improvements').border} ${getStatusColors('improvements').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('improvements').text}`}>
                <TrendingUp className={`h-5 w-5 ${getStatusColors('improvements').icon}`} />
                Areas for Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-2">
                <AlertCircle className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('improvements').icon}`} />
                <p className="text-sm leading-relaxed">{feedback.overall_assessment.areas_for_improvement}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Proctoring Assessment (if available) */}
      {feedback?.proctoring && (
        <Card className={`border-2 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).border} ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).text}`}>
              <AlertCircle className={`h-5 w-5 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).icon}`} />
              Proctoring Assessment
            </CardTitle>
            <CardDescription>
              Interview integrity and behavior analysis
            </CardDescription>
            <div className="mt-2 flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                feedback.proctoring.cheating_risk_level === 'LOW' ? 'bg-green-500' :
                feedback.proctoring.cheating_risk_level === 'MEDIUM' ? 'bg-yellow-500' :
                'bg-red-500'
              }`} />
              <span className="text-sm font-medium">
                Risk Level: {feedback.proctoring.cheating_risk_level}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(feedback.proctoring)
                .filter(([key]) => !['cheating_risk_level'].includes(key))
                .map(([key, value]: [string, any]) => (
                  <div key={key} className="rounded-lg border p-3">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium text-sm">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">{value}</p>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interview Transcript (if available) */}
      {feedback?.complete_transcript && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <FileText className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              Interview Transcript
            </CardTitle>
            <CardDescription>
              Complete record of the interview conversation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Duration:</span> {feedback.complete_transcript.total_duration}
                </div>
                <div>
                  <span className="font-medium">Questions:</span> {feedback.complete_transcript.question_count}
                </div>
              </div>
              <div className="max-h-96 overflow-y-auto rounded border bg-gray-50 p-4">
                <pre className="whitespace-pre-wrap text-sm">
                  {feedback.complete_transcript.questions_and_answers}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
