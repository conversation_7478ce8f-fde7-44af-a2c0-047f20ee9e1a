'use client';

import {
  <PERSON>ert<PERSON>ircle,
  Award,
  Bot,
  Brain,
  CheckCircle,
  FileText,
  MessageSquare,
  Star,
  Target,
  ThumbsDown,
  ThumbsUp,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Progress } from '@camped-ui/progress';

interface AIInterviewFeedbackDisplayProps {
  feedback: any;
  candidateName?: string;
  appliedRole?: string;
  appliedLevel?: string;
}

export const AIInterviewFeedbackDisplay = ({
  feedback,
  candidateName = 'Candidate',
  appliedRole = '',
  appliedLevel = '',
}: AIInterviewFeedbackDisplayProps) => {
  // Dynamic color scheme based on content/status
  const getStatusColors = (type: string, value?: any) => {
    switch (type) {
      case 'recommendation':
        const decision = value?.toLowerCase();
        if (decision?.includes('recommend') && !decision?.includes('not')) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (decision?.includes('consider')) {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }
      case 'proctoring':
        const level = value?.toLowerCase();
        if (level === 'low') {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else if (level === 'medium') {
          return {
            border: 'border-yellow-200',
            bg: 'bg-yellow-50/50',
            text: 'text-yellow-600',
            icon: 'text-yellow-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }
      case 'strengths':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/50',
          text: 'text-green-600',
          icon: 'text-green-600',
        };
      case 'improvements':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/50',
          text: 'text-red-600',
          icon: 'text-red-600',
        };
      case 'cultural_fit':
        const fit = value?.toLowerCase();
        if (fit?.includes('good') || fit?.includes('excellent') || fit?.includes('strong')) {
          return {
            border: 'border-green-200',
            bg: 'bg-green-50/50',
            text: 'text-green-600',
            icon: 'text-green-600',
          };
        } else {
          return {
            border: 'border-red-200',
            bg: 'bg-red-50/50',
            text: 'text-red-600',
            icon: 'text-red-600',
          };
        }
      case 'job_fit_score':
        if (value >= 80) {
          return { text: 'text-green-600' };
        } else if (value >= 60) {
          return { text: 'text-yellow-600' };
        } else {
          return { text: 'text-red-600' };
        }
      default:
        return {
          border: 'border-blue-200',
          bg: 'bg-blue-50/50',
          text: 'text-blue-600',
          icon: 'text-blue-600',
        };
    }
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getRecommendationIcon = (decision: string) => {
    const lowerDecision = decision?.toLowerCase();
    if (lowerDecision?.includes('recommend') && !lowerDecision?.includes('not')) {
      return <ThumbsUp className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    } else if (lowerDecision?.includes('consider')) {
      return <AlertCircle className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    } else {
      return <ThumbsDown className={`h-6 w-6 ${getStatusColors('recommendation', decision).icon}`} />;
    }
  };

  return (
    <div className="mx-auto w-full max-w-6xl space-y-6">
     

      {/* Hiring Recommendation - Enhanced */}
      {feedback?.hiring_recommendation && (
        <Card className={`border-2 ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).border} ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).bg}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getRecommendationIcon(feedback.hiring_recommendation.decision)}
                <div>
                  <CardTitle className="text-xl">AI Recommendation</CardTitle>
                  <CardDescription>
                    AI assessment for {candidateName}
                  </CardDescription>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getStatusColors('recommendation', feedback.hiring_recommendation.decision).text}`}>
                  {feedback.hiring_recommendation.decision}
                </div>
                <div className="text-sm text-gray-600">Final Decision</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="rounded-lg border bg-white p-4">
                <h4 className="mb-2 font-semibold">Reasoning</h4>
                <p className="leading-relaxed text-gray-700">{feedback.hiring_recommendation.reasoning}</p>
              </div>
              
              {feedback.hiring_recommendation.confidence_level && (
                <div className="rounded-lg border bg-white p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold">Confidence Level</span>
                    <Badge variant={getScoreBadgeVariant(feedback.hiring_recommendation.confidence_level)}>
                      {feedback.hiring_recommendation.confidence_level}%
                    </Badge>
                  </div>
                  <Progress value={feedback.hiring_recommendation.confidence_level} className="h-3" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

   
      {/* Job Fit Analysis - AI Specific */}
      {feedback?.job_fit && (
        <Card className={`border-2 ${getStatusColors('default').border} ${getStatusColors('default').bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('default').text}`}>
              <Target className={`h-5 w-5 ${getStatusColors('default').icon}`} />
              AI Job Fit Analysis
            </CardTitle>
            <CardDescription>
              Role-specific assessment for {appliedRole || 'the applied position'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(feedback.job_fit)
                .filter(([key]) => key !== 'Overall_score')
                .map(([key, value]: [string, any]) => (
                  <div key={key} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                      <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                    </div>
                    <Progress value={value.score} className="mb-3 h-3" />
                    <p className="text-sm text-gray-700">{value.feedback}</p>
                  </div>
                ))}
            </div>

            {/* Overall Job Fit Summary */}
            {feedback.job_fit.Overall_score && (
              <div className="mt-6 rounded-lg border-2 border-blue-200 bg-blue-50 p-4">
                <h4 className="mb-2 flex items-center gap-2 font-semibold text-blue-700">
                  <CheckCircle className="h-4 w-4" />
                  Overall Job Fit Assessment
                </h4>
                <p className="text-sm text-blue-700">{feedback.job_fit.Overall_score.feedback}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Core Values Assessment */}
      {feedback?.coreValue && (
        <Card className={`border-2 ${getStatusColors('cultural_fit', feedback.coreValue.Overall_fit?.decision).border} ${getStatusColors('cultural_fit', feedback.coreValue.Overall_fit?.decision).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('cultural_fit', feedback.coreValue.Overall_fit?.decision).text}`}>
              <Users className={`h-5 w-5 ${getStatusColors('cultural_fit', feedback.coreValue.Overall_fit?.decision).icon}`} />
              Core Values Assessment
            </CardTitle>
            <CardDescription>
              Alignment with organizational values and culture
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {Object.entries(feedback.coreValue)
                .filter(([key]) => !['Overall_fit', 'NOTE'].includes(key))
                .map(([key, value]: [string, any]) => (
                  <div key={key} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{key}</span>
                      <Badge variant={getScoreBadgeVariant(value.score)}>{value.score}/100</Badge>
                    </div>
                    <Progress value={value.score} className="mb-3 h-3" />
                    <p className="text-sm text-gray-700">{value.feedback}</p>
                  </div>
                ))}
            </div>

            {/* Overall Cultural Fit */}
            {feedback.coreValue.Overall_fit && (
              <div className="mt-6 rounded-lg border-2 border-purple-200 bg-purple-50 p-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="flex items-center gap-2 font-semibold text-purple-700">
                    <Users className="h-4 w-4" />
                    Overall Cultural Fit
                  </h4>
                  <Badge
                    variant={feedback.coreValue.Overall_fit.decision === 'fit' ? 'default' : 'destructive'}
                  >
                    {feedback.coreValue.Overall_fit.decision}
                  </Badge>
                </div>
                <p className="text-sm text-purple-700">{feedback.coreValue.Overall_fit.reason}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Strengths and Improvements - AI Specific */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {feedback?.strengths && (
          <Card className={`border-2 ${getStatusColors('strengths').border} ${getStatusColors('strengths').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('strengths').text}`}>
                <CheckCircle className={`h-5 w-5 ${getStatusColors('strengths').icon}`} />
                AI-Identified Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-2">
                <Star className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('strengths').icon}`} />
                <p className="text-sm leading-relaxed">{feedback.strengths}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {feedback?.areas_for_improvement && (
          <Card className={`border-2 ${getStatusColors('improvements').border} ${getStatusColors('improvements').bg}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${getStatusColors('improvements').text}`}>
                <TrendingUp className={`h-5 w-5 ${getStatusColors('improvements').icon}`} />
                AI-Suggested Improvements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-2">
                <Zap className={`mt-0.5 h-4 w-4 flex-shrink-0 ${getStatusColors('improvements').icon}`} />
                <p className="text-sm leading-relaxed">{feedback.areas_for_improvement}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Proctoring Assessment */}
      {feedback?.proctoring && (
        <Card className={`border-2 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).border} ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).text}`}>
              <AlertCircle className={`h-5 w-5 ${getStatusColors('proctoring', feedback.proctoring.cheating_risk_level).icon}`} />
              Proctoring Assessment
            </CardTitle>
            <CardDescription>
              Comprehensive analysis of interview integrity and candidate behavior
            </CardDescription>
            <div className="mt-2 flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                feedback.proctoring.cheating_risk_level === 'LOW' ? 'bg-green-500' :
                feedback.proctoring.cheating_risk_level === 'MEDIUM' ? 'bg-yellow-500' :
                feedback.proctoring.cheating_risk_level === 'HIGH' ? 'bg-orange-500' : 'bg-red-500'
              }`} />
              <Badge variant={
                feedback.proctoring.cheating_risk_level === 'LOW' ? 'default' :
                feedback.proctoring.cheating_risk_level === 'MEDIUM' ? 'secondary' : 'destructive'
              }>
                {feedback.proctoring.cheating_risk_level} RISK
              </Badge>
              {feedback.proctoring.ai_assistance_score && (
                <Badge variant="outline">
                  AI Assistance: {feedback.proctoring.ai_assistance_score}/100
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Key Proctoring Metrics */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {feedback.proctoring.camera_angle && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Camera Angle</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.camera_angle}</p>
                  </div>
                )}

                {feedback.proctoring.professional_attire && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Professional Attire</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.professional_attire}</p>
                  </div>
                )}

                {feedback.proctoring['eye_contact_&_posture'] && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Eye Contact & Posture</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring['eye_contact_&_posture']}</p>
                  </div>
                )}
              </div>

              {/* Behavioral Analysis */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {feedback.proctoring.response_authenticity && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Response Authenticity</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.response_authenticity}</p>
                  </div>
                )}

                {feedback.proctoring.emotional_state && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Emotional State</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.emotional_state}</p>
                  </div>
                )}

                {feedback.proctoring.candidate_movement && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Candidate Movement</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.candidate_movement}</p>
                  </div>
                )}

                {feedback.proctoring.background && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Background Analysis</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.background}</p>
                  </div>
                )}
              </div>

              {/* Critical Assessments */}
              <div className="space-y-4">
                {feedback.proctoring.suspicious_activity && (
                  <div className="rounded-lg border-2 border-orange-200 bg-orange-50 p-4">
                    <h4 className="mb-2 flex items-center gap-2 font-semibold text-orange-700">
                      <AlertCircle className="h-4 w-4" />
                      Suspicious Activity
                    </h4>
                    <p className="text-sm text-orange-600">{feedback.proctoring.suspicious_activity}</p>
                  </div>
                )}

                {feedback.proctoring.ai_assistance_indicators && (
                  <div className={`rounded-lg border-2 p-4 ${
                    (feedback.proctoring.ai_assistance_score || 0) > 50
                      ? 'border-red-200 bg-red-50'
                      : 'border-yellow-200 bg-yellow-50'
                  }`}>
                    <h4 className={`mb-2 flex items-center gap-2 font-semibold ${
                      (feedback.proctoring.ai_assistance_score || 0) > 50
                        ? 'text-red-700'
                        : 'text-yellow-700'
                    }`}>
                      <Brain className="h-4 w-4" />
                      AI Assistance Analysis
                      {feedback.proctoring.ai_assistance_score && (
                        <Badge variant={
                          feedback.proctoring.ai_assistance_score > 50 ? 'destructive' : 'secondary'
                        }>
                          {feedback.proctoring.ai_assistance_score}/100
                        </Badge>
                      )}
                    </h4>
                    <p className={`text-sm ${
                      (feedback.proctoring.ai_assistance_score || 0) > 50
                        ? 'text-red-600'
                        : 'text-yellow-600'
                    }`}>
                      {feedback.proctoring.ai_assistance_indicators}
                    </p>
                  </div>
                )}

                {feedback.proctoring.words_per_minute && (
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-semibold text-gray-700">Speech Analysis</h4>
                    <p className="text-sm text-gray-600">{feedback.proctoring.words_per_minute}</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Candidate Legitimacy Assessment */}
      {feedback?.candidate_legitimacy && (
        <Card className={`border-2 ${getStatusColors('proctoring', feedback.candidate_legitimacy.flag_level).border} ${getStatusColors('proctoring', feedback.candidate_legitimacy.flag_level).bg}`}>
          <CardHeader>
            <CardTitle className={`flex items-center gap-2 ${getStatusColors('proctoring', feedback.candidate_legitimacy.flag_level).text}`}>
              <AlertCircle className={`h-5 w-5 ${getStatusColors('proctoring', feedback.candidate_legitimacy.flag_level).icon}`} />
              Candidate Legitimacy Assessment
            </CardTitle>
            <CardDescription>
              AI-powered integrity and behavior analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="rounded-lg border-2 border-yellow-300 bg-yellow-100 p-4">
                <div className="mb-2 flex items-center justify-between">
                  <span className="font-semibold">Flag Level:</span>
                  <Badge
                    variant={
                      feedback.candidate_legitimacy.flag_level === 'None' || feedback.candidate_legitimacy.flag_level === 'Minimal'
                        ? 'default'
                        : feedback.candidate_legitimacy.flag_level === 'Moderate'
                        ? 'secondary'
                        : 'destructive'
                    }
                  >
                    {feedback.candidate_legitimacy.flag_level}
                  </Badge>
                </div>
                <p className="text-sm text-gray-700">
                  {feedback.candidate_legitimacy.reason}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interview Summary */}
      {feedback?.interview_summary && (
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <FileText className="h-5 w-5 text-blue-600" />
              Interview Summary
            </CardTitle>
            <CardDescription>
              Overview of topics discussed and key themes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {feedback.interview_summary.topics_discussed && (
              <div>
                <h4 className="font-semibold text-blue-700 mb-2">Topics Discussed:</h4>
                <p className="text-sm text-blue-600 leading-relaxed">{feedback.interview_summary.topics_discussed}</p>
              </div>
            )}
            {feedback.interview_summary.key_themes && (
              <div>
                <h4 className="font-semibold text-blue-700 mb-2">Key Themes:</h4>
                <p className="text-sm text-blue-600 leading-relaxed">{feedback.interview_summary.key_themes}</p>
              </div>
            )}
            {feedback.interview_summary.notable_moments && (
              <div>
                <h4 className="font-semibold text-blue-700 mb-2">Notable Moments:</h4>
                <p className="text-sm text-blue-600 leading-relaxed">{feedback.interview_summary.notable_moments}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Complete Transcript */}
      {feedback?.complete_transcript && (
        <Card className="border-2 border-gray-200 bg-gray-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-700">
              <MessageSquare className="h-5 w-5 text-gray-600" />
              Complete Interview Transcript
            </CardTitle>
            <CardDescription>
              Verbatim record of all questions and answers
              {feedback.complete_transcript.total_duration && (
                <span className="ml-2">• Duration: {feedback.complete_transcript.total_duration}</span>
              )}
              {feedback.complete_transcript.question_count && (
                <span className="ml-2">• Questions: {feedback.complete_transcript.question_count}</span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-h-96 overflow-y-auto rounded-lg border bg-white p-4">
              <pre className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed">
                {feedback.complete_transcript.questions_and_answers}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
