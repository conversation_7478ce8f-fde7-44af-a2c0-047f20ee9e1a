import React from 'react';

import { forwardRefIfNeeded } from '@/lib/forwardRefIfNeeded';
import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from '@camped-ui/lib';

const typographyVariants = cva('stack-scope text-md', {
  variants: {
    type: {
      h1: 'text-3xl font-bold',
      h2: 'text-2xl font-semibold',
      h3: 'text-xl font-medium',
      h4: 'text-lg font-medium',
      p: 'text-md',
      label: 'text-sm',
      footnote: 'text-xs',
    },
    variant: {
      primary: 'text-black dark:text-white',
      secondary: 'text-muted-foreground',
      destructive: 'text-destructive',
      success: 'text-green-500',
    },
  },
  defaultVariants: {
    type: 'p',
    variant: 'primary',
  },
});

type TypographyProps = {} & React.HTMLAttributes<HTMLHeadingElement> &
  VariantProps<typeof typographyVariants>;

const Typography = forwardRefIfNeeded<HTMLHeadingElement, TypographyProps>(
  ({ className, type, variant, ...props }, ref) => {
    const Comp = (type === 'footnote' || type === 'label' ? 'p' : type) || 'p';

    return (
      <Comp className={cn(typographyVariants({ type, variant, className }))} ref={ref} {...props} />
    );
  },
);
Typography.displayName = 'Typography';

export { Typography };
