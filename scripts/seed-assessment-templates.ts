import { PrismaClient } from '@prisma/client';
import { DEFAULT_TEMPLATES } from '../constants/assessment-templates';

const prisma = new PrismaClient();

async function seedAssessmentTemplates() {
  console.log('🌱 Seeding assessment templates...');

  try {
    // Create default assessment templates
    for (const template of DEFAULT_TEMPLATES) {
      const existingTemplate = await prisma.assessmentTemplate.findFirst({
        where: {
          name: template.name,
          roleType: template.roleType as any,
          isDefault: true,
        },
      });

      if (!existingTemplate) {
        await prisma.assessmentTemplate.create({
          data: {
            name: template.name,
            description: template.description,
            roleType: template.roleType as any,
            criteria: template.criteria,
            isDefault: template.isDefault,
            isActive: template.isActive,
          },
        });
        console.log(`✅ Created template: ${template.name}`);
      } else {
        console.log(`⏭️  Template already exists: ${template.name}`);
      }
    }

    console.log('🎉 Assessment templates seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding assessment templates:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this script is executed directly
if (require.main === module) {
  seedAssessmentTemplates()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedAssessmentTemplates };
