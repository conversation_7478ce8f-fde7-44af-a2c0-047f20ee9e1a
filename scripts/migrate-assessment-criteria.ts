#!/usr/bin/env ts-node

/**
 * Database Migration Script for Assessment Criteria
 * 
 * This script:
 * 1. Runs the Prisma migration to add assessment tables and fields
 * 2. Seeds default assessment templates
 * 3. Validates the migration was successful
 * 
 * Usage:
 *   npm run migrate:assessment
 *   or
 *   npx ts-node scripts/migrate-assessment-criteria.ts
 */

import { db } from '@/prisma/db';
import { execSync } from 'child_process';

// Using the extended db instance instead of creating a new PrismaClient

async function runMigration() {
  console.log('🚀 Starting Assessment Criteria Migration...\n');

  try {
    // Step 1: Run Prisma migration
    console.log('📦 Running Prisma migration...');
    execSync('npx prisma migrate deploy', { stdio: 'inherit' });
    console.log('✅ Prisma migration completed successfully\n');

    // Step 2: Verify database schema
    console.log('🔍 Verifying database schema...');
    
    // Check if AssessmentTemplate table exists
    const templateCount = await db.assessmentTemplate.count();
    console.log(`📊 Found ${templateCount} assessment templates`);

    // Check if EventDetails has new fields
    const eventDetails = await db.eventDetails.findFirst({
      select: {
        id: true,
        assessmentCriteria: true,
        assessmentTemplateId: true,
      }
    });
    
    if (eventDetails !== null) {
      console.log('✅ EventDetails table has assessment fields');
    } else {
      console.log('ℹ️  No existing EventDetails records found (this is normal for new installations)');
    }

    // Step 3: Verify default templates were created
    console.log('\n📋 Verifying default templates...');
    
    const defaultTemplates = await db.assessmentTemplate.findMany({
      where: { isDefault: true },
      select: {
        id: true,
        name: true,
        roleType: true,
        isActive: true,
      }
    });

    console.log(`✅ Found ${defaultTemplates.length} default templates:`);
    defaultTemplates.forEach(template => {
      console.log(`   - ${template.name} (${template.roleType})`);
    });

    // Step 4: Test template retrieval
    console.log('\n🧪 Testing template functionality...');
    
    const technicalTemplate = await db.assessmentTemplate.findFirst({
      where: {
        roleType: 'TECHNICAL',
        isDefault: true
      },
      include: {
        organization: true,
        createdBy: true,
      }
    });

    if (technicalTemplate) {
      const criteria = technicalTemplate.criteria as any[];
      console.log(`✅ Technical template has ${criteria.length} criteria`);
      console.log(`   Sample criterion: ${criteria[0]?.name || 'N/A'}`);
    }

    // Step 5: Generate migration summary
    console.log('\n📈 Migration Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`✅ AssessmentTemplate table: Created with ${templateCount} records`);
    console.log('✅ EventDetails.assessmentCriteria: Added (JSON field)');
    console.log('✅ EventDetails.assessmentTemplateId: Added (Foreign key)');
    console.log('✅ Assessment enums: Created (AssessmentRoleType, AssessmentScaleType)');
    console.log('✅ Foreign key constraints: Established');
    console.log('✅ Default templates: Seeded');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    console.log('\n🎉 Assessment Criteria Migration completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Update your application to use the new assessment features');
    console.log('   2. Test interview creation with custom assessment criteria');
    console.log('   3. Verify AI feedback generation with custom criteria');
    console.log('   4. Check the assessment templates in your admin panel');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure your database is running and accessible');
    console.log('   2. Check your DATABASE_URL environment variable');
    console.log('   3. Verify you have the necessary database permissions');
    console.log('   4. Run "npx prisma generate" if you see type errors');
    
    process.exit(1);
  } finally {
    // No need to disconnect when using the extended db instance
  }
}

async function rollbackMigration() {
  console.log('🔄 Rolling back Assessment Criteria Migration...\n');

  try {
    console.log('⚠️  WARNING: This will remove all assessment templates and criteria!');
    console.log('⚠️  This action cannot be undone!\n');

    // Remove assessment templates
    const deletedTemplates = await db.assessmentTemplate.deleteMany({});
    console.log(`🗑️  Deleted ${deletedTemplates.count} assessment templates`);

    // Note: We don't remove the columns as that would require a new migration
    console.log('ℹ️  Note: Database columns are preserved (requires new migration to remove)');
    
    console.log('\n✅ Rollback completed successfully!');

  } catch (error) {
    console.error('❌ Rollback failed:', error);
    process.exit(1);
  } finally {
    // No need to disconnect when using the extended db instance
  }
}

// Main execution
const command = process.argv[2];

if (command === 'rollback') {
  rollbackMigration();
} else {
  runMigration();
}

export { runMigration, rollbackMigration };
