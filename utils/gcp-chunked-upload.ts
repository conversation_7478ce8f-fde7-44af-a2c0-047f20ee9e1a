/**
 * GCP Resumable Upload Implementation
 * Based on: https://dev.to/kacase/resumable-multi-chunk-upload-to-gcp-bucket-1chk
 */

export interface GCPUploadOptions {
  file: Blob;
  fileName: string;
  chunkSize?: number;
  onProgress?: (progress: number) => void;
  onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
  maxRetries?: number;
}

export interface GCPUploadResult {
  success: boolean;
  error?: string;
  uploadTime?: number;
}

const DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024; // 2MB chunks (Vercel supports up to 4.5MB)
const DEFAULT_MAX_RETRIES = 3;

export class GCPResumableUploader {
  private file: Blob;
  private fileName: string;
  private chunkSize: number;
  private onProgress?: (progress: number) => void;
  private onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
  private maxRetries: number;
  private uploadId?: string;

  constructor(options: GCPUploadOptions) {
    this.file = options.file;
    this.fileName = options.fileName;
    this.chunkSize = options.chunkSize || DEFAULT_CHUNK_SIZE;
    this.onProgress = options.onProgress;
    this.onChunkComplete = options.onChunkComplete;
    this.maxRetries = options.maxRetries || DEFAULT_MAX_RETRIES;
  }

  async upload(): Promise<GCPUploadResult> {
    const startTime = Date.now();
    console.log('🎬 Starting GCP upload for:', this.fileName, 'Size:', this.file.size);

    try {
      // Step 1: Initiate resumable upload
      console.log('📋 Step 1: Initiating upload...');
      await this.initiateUpload();

      if (!this.uploadId) {
        throw new Error('Failed to get upload ID');
      }

      // Step 2: Upload file in chunks
      console.log('📤 Step 2: Starting chunked upload...');
      await this.uploadInChunks();

      console.log('✅ Upload completed successfully!');
      return {
        success: true,
        uploadTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('❌ GCP upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        uploadTime: Date.now() - startTime,
      };
    }
  }

  private async initiateUpload(): Promise<void> {
    console.log('🚀 Initiating GCP resumable upload for file:', this.fileName);

    const response = await fetch('/api/gcp-resumable-upload?action=initiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Upload-Content-Length': this.file.size.toString(),
        'X-Upload-Content-Type': 'video/mp4',
      },
      body: JSON.stringify({
        fileName: this.fileName,
      }),
    });

    console.log('📡 Initiate response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Failed to initiate upload:', errorText);
      throw new Error(`Failed to initiate upload: ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Upload initiated successfully:', data);
    this.uploadId = data.uploadId;
    // Use server proxy to avoid CORS issues
  }

  private async uploadInChunks(): Promise<void> {
    const totalChunks = Math.ceil(this.file.size / this.chunkSize);
    let uploadedBytes = 0;

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * this.chunkSize;
      const end = Math.min(start + this.chunkSize, this.file.size);
      const chunk = this.file.slice(start, end);

      await this.uploadChunkWithRetry(chunk, start, end - 1, chunkIndex, totalChunks);

      uploadedBytes += chunk.size;
      const progress = Math.round((uploadedBytes / this.file.size) * 100);
      this.onProgress?.(progress);
      this.onChunkComplete?.(chunkIndex, totalChunks);
    }
  }

  private async uploadChunkWithRetry(
    chunk: Blob,
    start: number,
    end: number,
    chunkIndex: number,
    totalChunks: number,
  ): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        await this.uploadChunk(chunk, start, end);
        return; // Success
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(
          `Chunk ${chunkIndex} upload attempt ${attempt + 1} failed:`,
          lastError.message,
        );

        if (attempt < this.maxRetries - 1) {
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw (
      lastError ||
      new Error(`Failed to upload chunk ${chunkIndex} after ${this.maxRetries} attempts`)
    );
  }

  private async uploadChunk(chunk: Blob, start: number, end: number): Promise<void> {
    if (!this.uploadId) {
      throw new Error('Upload ID not available');
    }

    const contentRange = `bytes ${start}-${end}/${this.file.size}`;
    console.log(`📦 Uploading chunk: ${contentRange}, Size: ${chunk.size} bytes`);

    // Use server proxy to avoid CORS issues (2MB chunks work with Vercel)
    const response = await fetch(
      `/api/gcp-resumable-upload?action=upload&uploadId=${encodeURIComponent(this.uploadId)}`,
      {
        method: 'PUT',
        headers: {
          'Content-Range': contentRange,
          'Content-Length': chunk.size.toString(),
          'Content-Type': 'video/mp4',
        },
        body: chunk,
      },
    );

    console.log(`📡 Chunk upload response: ${response.status}`);

    if (response.status === 308) {
      // Upload incomplete, continue with next chunk
      return;
    } else if (response.status >= 200 && response.status < 300) {
      // Upload complete or chunk accepted
      return;
    } else {
      const errorText = await response.text();
      console.error(`❌ Upload failed: ${response.status} - ${errorText}`);

      if (response.status === 413) {
        throw new Error(
          `Chunk size too large (${chunk.size} bytes). Server cannot handle this request size.`,
        );
      } else {
        throw new Error(`Upload failed with status ${response.status}: ${errorText}`);
      }
    }
  }

  async getUploadStatus(): Promise<{ complete: boolean; range?: string }> {
    if (!this.uploadId) {
      throw new Error('Upload ID not available');
    }

    // Use server proxy to check status
    const response = await fetch(
      `/api/gcp-resumable-upload?action=status&uploadId=${encodeURIComponent(this.uploadId)}`,
      {
        method: 'PUT',
        headers: {
          'Content-Range': 'bytes */*',
        },
      },
    );

    const range = response.headers.get('range');

    return {
      complete: response.status >= 200 && response.status < 300,
      range: range || undefined,
    };
  }
}

// Utility function for easy GCP resumable upload
export const uploadVideoToGCP = async (
  file: Blob,
  fileName: string,
  options?: Partial<GCPUploadOptions>,
): Promise<GCPUploadResult> => {
  const uploader = new GCPResumableUploader({
    file,
    fileName,
    ...options,
  });

  return uploader.upload();
};
