import os from 'os';
import path from 'path';

/**
 * Configuration for video processing operations
 */
export interface VideoProcessingConfig {
  tempDir: string;
  maxFileSizeMB: number;
  timeoutMinutes: number;
  bucketName: string;
  gcpCredentials: {
    projectId: string;
    privateKey: string;
    clientId: string;
    clientEmail: string;
  };
  ffmpegSettings: {
    preset: string;
    outputFormat: string;
    videoCodec: string;
    audioCodec: string;
  };
}

/**
 * Get video processing configuration from environment variables
 */
export function getVideoProcessingConfig(): VideoProcessingConfig {
  // Temporary directory for video processing
  const tempDir = process.env.VIDEO_CONVERSION_TEMP_DIR || 
                  path.join(os.tmpdir(), 'aceprep-video-conversion');

  // Maximum file size in MB
  const maxFileSizeMB = parseInt(process.env.VIDEO_CONVERSION_MAX_SIZE_MB || '500');

  // Timeout in minutes
  const timeoutMinutes = parseInt(process.env.VIDEO_CONVERSION_TIMEOUT_MINUTES || '10');

  // GCP bucket name for tailored practice videos
  const bucketName = 'aceprep'; // Hardcoded as per requirements

  // GCP credentials
  const gcpCredentials = {
    projectId: process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '',
    privateKey: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
    clientId: process.env.NEXT_PUBLIC_CLIENT_ID || '',
    clientEmail: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
  };

  // FFmpeg settings optimized for HLS to MP4 conversion
  const ffmpegSettings = {
    preset: 'fast', // Balance between speed and quality
    outputFormat: 'mp4',
    videoCodec: 'copy', // Copy video stream without re-encoding for speed
    audioCodec: 'aac', // Ensure AAC audio for MP4 compatibility
  };

  return {
    tempDir,
    maxFileSizeMB,
    timeoutMinutes,
    bucketName,
    gcpCredentials,
    ffmpegSettings,
  };
}

/**
 * Validate video processing configuration
 */
export function validateVideoProcessingConfig(config: VideoProcessingConfig): string[] {
  const errors: string[] = [];

  if (!config.gcpCredentials.projectId) {
    errors.push('NEXT_PUBLIC_GCP_PROJECT_ID is required');
  }

  if (!config.gcpCredentials.privateKey) {
    errors.push('NEXT_PUBLIC_PRIVATE_KEY is required');
  }

  if (!config.gcpCredentials.clientId) {
    errors.push('NEXT_PUBLIC_CLIENT_ID is required');
  }

  if (!config.gcpCredentials.clientEmail) {
    errors.push('NEXT_PUBLIC_CLIENT_EMAIL is required');
  }

  if (config.maxFileSizeMB <= 0) {
    errors.push('VIDEO_CONVERSION_MAX_SIZE_MB must be greater than 0');
  }

  if (config.timeoutMinutes <= 0) {
    errors.push('VIDEO_CONVERSION_TIMEOUT_MINUTES must be greater than 0');
  }

  return errors;
}

/**
 * Get FFmpeg arguments for HLS to MP4 conversion
 */
export function getFFmpegArgs(inputPath: string, outputPath: string): string[] {
  const config = getVideoProcessingConfig();
  
  return [
    '-i', inputPath,
    '-c:v', config.ffmpegSettings.videoCodec, // Copy video stream
    '-c:a', config.ffmpegSettings.audioCodec, // AAC audio
    '-bsf:a', 'aac_adtstoasc', // Fix AAC stream for MP4 container
    '-movflags', '+faststart', // Optimize for web streaming
    '-preset', config.ffmpegSettings.preset, // Encoding preset
    '-y', // Overwrite output file
    outputPath
  ];
}

/**
 * Check if FFmpeg is available on the system
 */
export async function checkFFmpegAvailability(): Promise<boolean> {
  try {
    const { spawn } = await import('child_process');
    
    return new Promise((resolve) => {
      const ffmpeg = spawn('ffmpeg', ['-version']);
      
      ffmpeg.on('close', (code) => {
        resolve(code === 0);
      });
      
      ffmpeg.on('error', () => {
        resolve(false);
      });
    });
  } catch (error) {
    return false;
  }
}

/**
 * Get estimated conversion time based on video duration
 * This is a rough estimate and actual time may vary
 */
export function estimateConversionTime(videoDurationSeconds: number): number {
  // Rough estimate: 1 second of video takes ~0.1 seconds to convert with copy codec
  // Add 30 seconds overhead for download/upload operations
  return Math.max(videoDurationSeconds * 0.1 + 30, 60); // Minimum 1 minute
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * Check if file size is within limits
 */
export function isFileSizeValid(sizeBytes: number): boolean {
  const config = getVideoProcessingConfig();
  const maxSizeBytes = config.maxFileSizeMB * 1024 * 1024;
  return sizeBytes <= maxSizeBytes;
}

/**
 * Get timeout in milliseconds
 */
export function getTimeoutMs(): number {
  const config = getVideoProcessingConfig();
  return config.timeoutMinutes * 60 * 1000;
}
