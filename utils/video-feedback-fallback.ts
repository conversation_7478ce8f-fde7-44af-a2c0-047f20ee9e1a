/**
 * Fallback responses for video feedback when processing fails
 * These provide meaningful feedback to users even when video analysis is not possible
 */

export interface FallbackFeedback {
  transcript: string;
  overall_score: number;
  short_summary: string;
  communication_skills: {
    clarity: number;
    pace: number;
    confidence: number;
    eye_contact: number;
    body_language: number;
  };
  proctoring_assessment: {
    camera_angle: string;
    professional_attire: string;
    eye_contact_rating: string;
    posture_assessment: string;
  };
  interview_performance: {
    content_quality: number;
    technical_accuracy: number;
    problem_solving: number;
    examples_provided: number;
  };
  recommendations: string[];
  areas_for_improvement: string[];
}

/**
 * Generate fallback feedback when video processing fails
 */
export function generateFallbackFeedback(
  question: string,
  role?: string,
  level?: string,
  errorType?: string
): FallbackFeedback {
  const baseRecommendations = [
    "Practice your response to this question multiple times to improve fluency",
    "Record yourself answering similar questions to review your performance",
    "Focus on providing specific examples from your experience",
    "Ensure good lighting and camera positioning for future recordings"
  ];

  const baseImprovements = [
    "Video quality and audio clarity",
    "Structured response organization",
    "Use of specific examples and metrics",
    "Professional presentation setup"
  ];

  // Customize feedback based on error type
  let specificMessage = "We were unable to analyze your video response due to technical issues.";
  let specificRecommendations = [...baseRecommendations];

  switch (errorType) {
    case 'file_not_found':
      specificMessage = "Your video recording could not be found for analysis.";
      specificRecommendations.unshift("Ensure your video is properly uploaded before requesting feedback");
      break;
    
    case 'conversion_error':
      specificMessage = "There was an issue processing your video format.";
      specificRecommendations.unshift("Try recording in a standard video format (MP4) for better compatibility");
      break;
    
    case 'timeout_error':
      specificMessage = "Your video took too long to process, possibly due to file size or complexity.";
      specificRecommendations.unshift("Consider recording shorter responses or reducing video quality");
      break;
    
    case 'file_too_large':
      specificMessage = "Your video file is too large for processing.";
      specificRecommendations.unshift("Record shorter responses or compress your video before uploading");
      break;
    
    case 'system_error':
      specificMessage = "Our video processing system is temporarily unavailable.";
      specificRecommendations.unshift("Please try again in a few minutes");
      break;
  }

  return {
    transcript: "Unable to generate transcript due to video processing issues.",
    overall_score: 0,
    short_summary: `${specificMessage} However, here are some general recommendations for improving your interview responses.`,
    communication_skills: {
      clarity: 0,
      pace: 0,
      confidence: 0,
      eye_contact: 0,
      body_language: 0,
    },
    proctoring_assessment: {
      camera_angle: "Unable to assess - ensure camera is at eye level",
      professional_attire: "Unable to assess - dress professionally for interviews",
      eye_contact_rating: "Unable to assess - maintain eye contact with camera",
      posture_assessment: "Unable to assess - sit up straight and maintain good posture",
    },
    interview_performance: {
      content_quality: 0,
      technical_accuracy: 0,
      problem_solving: 0,
      examples_provided: 0,
    },
    recommendations: specificRecommendations,
    areas_for_improvement: baseImprovements,
  };
}

/**
 * Generate role-specific fallback recommendations
 */
export function getRoleSpecificRecommendations(role: string): string[] {
  const recommendations: Record<string, string[]> = {
    'Software Engineer': [
      "Practice explaining technical concepts in simple terms",
      "Prepare examples of your coding projects and problem-solving approach",
      "Review common algorithms and data structures",
      "Practice whiteboarding or coding exercises"
    ],
    'Product Manager': [
      "Prepare examples of product decisions you've made",
      "Practice explaining complex product concepts clearly",
      "Review product metrics and KPIs you've worked with",
      "Prepare stories about cross-functional collaboration"
    ],
    'Data Scientist': [
      "Practice explaining statistical concepts to non-technical audiences",
      "Prepare examples of data projects and their business impact",
      "Review machine learning algorithms and their applications",
      "Practice presenting data insights clearly"
    ],
    'Marketing Manager': [
      "Prepare examples of successful marketing campaigns",
      "Practice explaining marketing metrics and ROI",
      "Review digital marketing trends and strategies",
      "Prepare stories about brand management and customer acquisition"
    ],
    'Sales Representative': [
      "Practice your sales pitch and objection handling",
      "Prepare examples of successful deals and client relationships",
      "Review sales methodologies and CRM tools",
      "Practice demonstrating products or services"
    ]
  };

  return recommendations[role] || [
    "Research the company and role thoroughly",
    "Prepare specific examples from your experience",
    "Practice common interview questions for your field",
    "Review industry trends and best practices"
  ];
}

/**
 * Generate level-specific fallback recommendations
 */
export function getLevelSpecificRecommendations(level: string): string[] {
  const recommendations: Record<string, string[]> = {
    'Entry Level': [
      "Focus on your educational projects and internship experiences",
      "Demonstrate your eagerness to learn and grow",
      "Highlight relevant coursework and certifications",
      "Show enthusiasm for the role and company"
    ],
    'Mid Level': [
      "Emphasize your professional accomplishments and impact",
      "Prepare examples of leadership and mentoring experiences",
      "Discuss how you've grown in your current role",
      "Show your ability to work independently and lead projects"
    ],
    'Senior Level': [
      "Focus on strategic thinking and business impact",
      "Prepare examples of team leadership and organizational influence",
      "Discuss your vision for the role and team",
      "Demonstrate your ability to mentor and develop others"
    ],
    'Executive Level': [
      "Emphasize your strategic vision and business transformation experience",
      "Prepare examples of organizational leadership and change management",
      "Discuss your approach to building and scaling teams",
      "Show your ability to drive company-wide initiatives"
    ]
  };

  return recommendations[level] || [
    "Tailor your examples to the appropriate level of responsibility",
    "Demonstrate growth and progression in your career",
    "Show understanding of the role's requirements and challenges",
    "Prepare questions that show your strategic thinking"
  ];
}

/**
 * Create a comprehensive fallback response with role and level considerations
 */
export function createComprehensiveFallbackFeedback(
  question: string,
  role?: string,
  level?: string,
  errorType?: string
): FallbackFeedback {
  const baseFeedback = generateFallbackFeedback(question, role, level, errorType);
  
  // Add role-specific recommendations
  if (role) {
    const roleRecommendations = getRoleSpecificRecommendations(role);
    baseFeedback.recommendations.push(...roleRecommendations);
  }
  
  // Add level-specific recommendations
  if (level) {
    const levelRecommendations = getLevelSpecificRecommendations(level);
    baseFeedback.recommendations.push(...levelRecommendations);
  }
  
  // Remove duplicates and limit to reasonable number
  baseFeedback.recommendations = [...new Set(baseFeedback.recommendations)].slice(0, 8);
  
  return baseFeedback;
}
