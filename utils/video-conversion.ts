import { Storage } from '@google-cloud/storage';
import { spawn } from 'child_process';
import fs from 'fs';
import os from 'os';
import path from 'path';

import Audit from '@/lib/audit';
import {
  getVideoProcessingConfig,
  validateVideoProcessingConfig,
  getFFmpegArgs,
  checkFFmpegAvailability,
  isFileSizeValid,
  formatFileSize,
  getTimeoutMs
} from './video-processing-config';

export interface VideoConversionOptions {
  practiceId: string;
  organizationId?: string;
  userId?: string;
  onProgress?: (progress: number) => void;
}

export interface VideoConversionResult {
  success: boolean;
  mp4FilePath?: string;
  error?: string;
  conversionTime?: number;
  originalSize?: number;
  convertedSize?: number;
}

/**
 * Converts HLS playlist (.m3u8) to MP4 format for Gemini Vision API compatibility
 * Downloads HLS segments and concatenates them into a single MP4 file
 */
export class HLSToMP4Converter {
  private storage: Storage;
  private tempDir: string;
  private bucketName: string;

  constructor() {
    // Get configuration
    const config = getVideoProcessingConfig();

    // Validate configuration
    const configErrors = validateVideoProcessingConfig(config);
    if (configErrors.length > 0) {
      throw new Error(`Video processing configuration errors: ${configErrors.join(', ')}`);
    }

    // Initialize Google Cloud Storage client
    this.storage = new Storage({
      projectId: config.gcpCredentials.projectId,
      keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
      credentials: {
        private_key: config.gcpCredentials.privateKey,
        client_id: config.gcpCredentials.clientId,
        client_email: config.gcpCredentials.clientEmail,
      },
    });

    // Use the configured bucket name for tailored practice videos
    this.bucketName = config.bucketName;

    // Create temporary directory for video processing
    this.tempDir = config.tempDir;
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * Convert HLS playlist to MP4
   */
  async convertHLSToMP4(options: VideoConversionOptions): Promise<VideoConversionResult> {
    const startTime = Date.now();
    const { practiceId, organizationId, userId, onProgress } = options;

    let tempPlaylistPath: string | null = null;
    let outputPath: string | null = null;

    try {
      // Check FFmpeg availability
      const ffmpegAvailable = await checkFFmpegAvailability();
      if (!ffmpegAvailable) {
        throw new Error('FFmpeg is not available on this system');
      }

      // Log conversion start
      Audit.logEvent({
        action: 'hls_to_mp4_conversion_started',
        userId: userId || 'system',
        details: {
          screen: 'tailored-practice-video-feedback',
          practiceId,
          organizationId,
          bucketName: this.bucketName,
        },
      });

      onProgress?.(10);

      // Download HLS playlist from GCP bucket
      const playlistPath = `interview-ai/${practiceId}.m3u8`;
      tempPlaylistPath = path.join(this.tempDir, `${practiceId}_playlist.m3u8`);
      
      console.log(`Downloading HLS playlist: gs://${this.bucketName}/${playlistPath}`);
      
      await this.downloadFile(playlistPath, tempPlaylistPath);
      onProgress?.(30);

      // Read and modify playlist to use local paths
      const modifiedPlaylistPath = await this.preparePlaylistForConversion(
        tempPlaylistPath, 
        practiceId
      );
      onProgress?.(50);

      // Convert to MP4 using FFmpeg
      outputPath = path.join(this.tempDir, `${practiceId}_converted.mp4`);
      await this.runFFmpegConversion(modifiedPlaylistPath, outputPath, onProgress);
      onProgress?.(90);

      // Get file sizes for logging
      const originalSize = await this.getPlaylistTotalSize(practiceId);
      const convertedSize = fs.statSync(outputPath).size;
      const conversionTime = Date.now() - startTime;

      // Log successful conversion
      Audit.logEvent({
        action: 'hls_to_mp4_conversion_completed',
        userId: userId || 'system',
        details: {
          screen: 'tailored-practice-video-feedback',
          practiceId,
          organizationId,
          conversionTime,
          originalSize,
          convertedSize,
          compressionRatio: originalSize ? (convertedSize / originalSize).toFixed(2) : 'N/A',
        },
      });

      onProgress?.(100);

      return {
        success: true,
        mp4FilePath: outputPath,
        conversionTime,
        originalSize,
        convertedSize,
      };

    } catch (error) {
      console.error('HLS to MP4 conversion failed:', error);

      // Determine error type for better user feedback
      let errorType = 'unknown_error';
      let userFriendlyMessage = 'Video conversion failed due to an unexpected error.';

      if (error.message.includes('not found')) {
        errorType = 'file_not_found';
        userFriendlyMessage = 'Video file not found. Please ensure the video was uploaded correctly.';
      } else if (error.message.includes('FFmpeg')) {
        errorType = 'conversion_error';
        userFriendlyMessage = 'Video format conversion failed. Please try again or contact support.';
      } else if (error.message.includes('timed out')) {
        errorType = 'timeout_error';
        userFriendlyMessage = 'Video conversion timed out. The video may be too large or complex.';
      } else if (error.message.includes('size') && error.message.includes('exceeds')) {
        errorType = 'file_too_large';
        userFriendlyMessage = 'Video file is too large for processing.';
      } else if (error.message.includes('FFmpeg is not available')) {
        errorType = 'system_error';
        userFriendlyMessage = 'Video processing system is temporarily unavailable.';
      }

      // Log conversion failure with detailed information
      Audit.logEvent({
        action: 'hls_to_mp4_conversion_failed',
        userId: userId || 'system',
        details: {
          screen: 'tailored-practice-video-feedback',
          practiceId,
          organizationId,
          error: error.message,
          errorType,
          conversionTime: Date.now() - startTime,
          bucketName: this.bucketName,
          tempDir: this.tempDir,
        },
      });

      // Cleanup on error
      this.cleanup([tempPlaylistPath, outputPath].filter(Boolean));

      return {
        success: false,
        error: userFriendlyMessage,
        conversionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Download file from GCP bucket
   */
  private async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      const file = this.storage.bucket(this.bucketName).file(remotePath);
      const [exists] = await file.exists();
      
      if (!exists) {
        throw new Error(`File not found: gs://${this.bucketName}/${remotePath}`);
      }

      await file.download({ destination: localPath });
      console.log(`Downloaded: ${remotePath} -> ${localPath}`);
    } catch (error) {
      throw new Error(`Failed to download ${remotePath}: ${error.message}`);
    }
  }

  /**
   * Prepare playlist for conversion by downloading segments and updating paths
   */
  private async preparePlaylistForConversion(
    playlistPath: string, 
    practiceId: string
  ): Promise<string> {
    const playlistContent = fs.readFileSync(playlistPath, 'utf8');
    const lines = playlistContent.split('\n');
    const modifiedLines: string[] = [];
    
    for (const line of lines) {
      if (line.endsWith('.ts')) {
        // Download the .ts segment file
        const segmentName = line.trim();
        const remotePath = `interview-ai/${segmentName}`;
        const localPath = path.join(this.tempDir, `${practiceId}_${segmentName}`);
        
        await this.downloadFile(remotePath, localPath);
        modifiedLines.push(localPath);
      } else {
        modifiedLines.push(line);
      }
    }

    // Write modified playlist
    const modifiedPlaylistPath = path.join(this.tempDir, `${practiceId}_modified.m3u8`);
    fs.writeFileSync(modifiedPlaylistPath, modifiedLines.join('\n'));

    return modifiedPlaylistPath;
  }

  /**
   * Run FFmpeg conversion from HLS to MP4
   */
  private async runFFmpegConversion(
    inputPath: string,
    outputPath: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      // Get FFmpeg arguments from configuration
      const ffmpegArgs = getFFmpegArgs(inputPath, outputPath);

      console.log(`Running FFmpeg: ffmpeg ${ffmpegArgs.join(' ')}`);

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);
      let stderr = '';

      // Set timeout for FFmpeg process
      const timeout = setTimeout(() => {
        ffmpeg.kill('SIGKILL');
        reject(new Error('FFmpeg conversion timed out'));
      }, getTimeoutMs());

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();

        // Parse FFmpeg progress if possible
        const timeMatch = stderr.match(/time=(\d{2}):(\d{2}):(\d{2})/);
        if (timeMatch && onProgress) {
          // Simple progress estimation (this is rough)
          const currentSeconds = parseInt(timeMatch[1]) * 3600 +
                               parseInt(timeMatch[2]) * 60 +
                               parseInt(timeMatch[3]);
          // Assume 5 minutes max video length for progress calculation
          const progress = Math.min(50 + (currentSeconds / 300) * 40, 90);
          onProgress(progress);
        }
      });

      ffmpeg.on('close', (code) => {
        clearTimeout(timeout);

        if (code === 0) {
          console.log('FFmpeg conversion completed successfully');
          resolve();
        } else {
          console.error('FFmpeg conversion failed with code:', code);
          console.error('FFmpeg stderr:', stderr);
          reject(new Error(`FFmpeg conversion failed with exit code ${code}`));
        }
      });

      ffmpeg.on('error', (error) => {
        clearTimeout(timeout);
        console.error('FFmpeg spawn error:', error);
        reject(new Error(`Failed to spawn FFmpeg: ${error.message}`));
      });
    });
  }

  /**
   * Get total size of HLS playlist and segments
   */
  private async getPlaylistTotalSize(practiceId: string): Promise<number> {
    try {
      let totalSize = 0;

      // Get playlist file size
      const playlistFile = this.storage.bucket(this.bucketName).file(`interview-ai/${practiceId}.m3u8`);
      const [playlistExists] = await playlistFile.exists();

      if (!playlistExists) {
        throw new Error(`Playlist file not found: interview-ai/${practiceId}.m3u8`);
      }

      const [playlistMetadata] = await playlistFile.getMetadata();
      totalSize += parseInt(playlistMetadata.size || '0');

      // Get all .ts files for this practice
      const [files] = await this.storage.bucket(this.bucketName).getFiles({
        prefix: `interview-ai/${practiceId}`,
      });

      for (const file of files) {
        if (file.name.endsWith('.ts')) {
          const [metadata] = await file.getMetadata();
          const fileSize = parseInt(metadata.size || '0');
          totalSize += fileSize;
        }
      }

      // Validate total size
      if (!isFileSizeValid(totalSize)) {
        throw new Error(`Video size ${formatFileSize(totalSize)} exceeds maximum allowed size`);
      }

      return totalSize;
    } catch (error) {
      console.error('Error calculating playlist size:', error);
      throw error;
    }
  }

  /**
   * Upload converted MP4 to GCP bucket for Gemini processing
   */
  async uploadConvertedVideo(localPath: string, practiceId: string): Promise<string> {
    try {
      const remotePath = `interview-ai/converted/${practiceId}.mp4`;
      const file = this.storage.bucket(this.bucketName).file(remotePath);

      await file.save(fs.readFileSync(localPath), {
        metadata: {
          contentType: 'video/mp4',
        },
      });

      console.log(`Uploaded converted video: gs://${this.bucketName}/${remotePath}`);
      return remotePath;
    } catch (error) {
      throw new Error(`Failed to upload converted video: ${error.message}`);
    }
  }

  /**
   * Cleanup temporary files
   */
  cleanup(filePaths: string[]): void {
    for (const filePath of filePaths) {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up: ${filePath}`);
        }
      } catch (error) {
        console.error(`Failed to cleanup ${filePath}:`, error);
      }
    }
  }

  /**
   * Cleanup all temporary files for a practice ID
   */
  cleanupPracticeFiles(practiceId: string): void {
    try {
      const files = fs.readdirSync(this.tempDir);
      const practiceFiles = files.filter(file => file.includes(practiceId));

      for (const file of practiceFiles) {
        const filePath = path.join(this.tempDir, file);
        fs.unlinkSync(filePath);
        console.log(`Cleaned up practice file: ${filePath}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup practice files for ${practiceId}:`, error);
    }
  }
}

/**
 * Utility function to convert HLS to MP4 for a practice ID
 */
export async function convertTailoredPracticeVideo(
  practiceId: string,
  options: Partial<VideoConversionOptions> = {}
): Promise<VideoConversionResult> {
  const converter = new HLSToMP4Converter();

  try {
    const result = await converter.convertHLSToMP4({
      practiceId,
      ...options,
    });

    // Upload converted video to GCP if conversion was successful
    if (result.success && result.mp4FilePath) {
      try {
        const remotePath = await converter.uploadConvertedVideo(result.mp4FilePath, practiceId);

        // Cleanup local files after successful upload
        converter.cleanup([result.mp4FilePath]);
        converter.cleanupPracticeFiles(practiceId);

        return {
          ...result,
          mp4FilePath: `gs://${converter['bucketName']}/${remotePath}`,
        };
      } catch (uploadError) {
        console.error('Failed to upload converted video:', uploadError);
        // Still return success but with local path
        return result;
      }
    }

    return result;
  } catch (error) {
    // Cleanup on any error
    converter.cleanupPracticeFiles(practiceId);
    throw error;
  }
}
