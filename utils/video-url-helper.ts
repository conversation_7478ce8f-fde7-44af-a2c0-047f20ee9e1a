/**
 * Utility functions for handling video URLs and upload for video interviews
 */
import { uploadVideoToGCP } from './gcp-chunked-upload';

/**
 * Simple synchronous function to get video URL using CloudFront
 * This is used for basic video URL construction without async operations
 * @param videoUrl - The video file name/path
 * @param videoOrigin - The video origin (unused for compatibility)
 * @param organizationId - Organization ID (unused for compatibility)
 * @returns string - The CloudFront video URL
 */
export function getVideoUrl(
  videoUrl?: string,
  videoOrigin?: string,
  organizationId?: string,
): string {
  if (!videoUrl) {
    return '';
  }

  // For video interviews, construct CloudFront URL
  // This follows the pattern used throughout the codebase
  return `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/video-meeting/${videoUrl}`;
}

/**
 * Uploads a video file using GCP resumable upload
 * @param file - The video file to upload
 * @param fileName - The name for the uploaded file
 * @param options - Upload options including progress callbacks
 * @returns Promise with upload result
 */
export async function uploadVideoWithCORSSupport(
  file: Blob,
  fileName: string,
  options?: {
    onProgress?: (progress: number) => void;
    onChunkComplete?: (chunkIndex: number, totalChunks: number) => void;
  },
) {
  return uploadVideoToGCP(file, fileName, options);
}

/**
 * Gets video URL with GCP bucket first, CloudFront fallback logic
 * Note: Video interviews specifically use GCP by default
 * @param s3RecordingId - The recording ID
 * @param videoOrigin - The video origin (GCP_BUCKET or null for CloudFront fallback)
 * @param organizationId - Organization ID for tenant-specific bucket
 * @param isAiInterview - Whether this is an AI interview (uses different folder structure)
 * @returns Promise<string> - The video URL to use
 */
export async function getVideoUrlWithFallback(
  s3RecordingId?: string,
  videoOrigin?: string,
  organizationId?: string,
  isAiInterview: boolean = false,
): Promise<string> {
  if (!s3RecordingId) {
    return '';
  }

  try {
    // For video interviews, always try GCP first (videoOrigin is undefined for video interviews)
    // For other interview types, respect the videoOrigin setting
    if (videoOrigin === 'GCP_BUCKET' || videoOrigin === undefined) {
      try {
        // Construct the proper file path for video meetings
        // Video meetings are stored in: recordings/video-meeting/filename
        const folder = isAiInterview
          ? ''
          : `${process.env.NEXT_PUBLIC_S3FOLDER || 'recordings'}/video-meeting`;
        const filePath = folder ? `${folder}/${s3RecordingId}` : s3RecordingId;

        // Use API endpoint to get signed URL from GCP
        const response = await fetch(
          `/api/gcp-bucket?id=${encodeURIComponent(filePath)}&organizationId=${
            organizationId || ''
          }`,
        );
        if (response.ok) {
          const data = await response.json();
          const gcpUrl = data.signedUrl;

          // Return the signed URL directly for video playback
          return gcpUrl;
        }
      } catch (error) {
        console.log('GCP bucket access failed, falling back to CloudFront:', error);
      }
    }

    // Fallback: CloudFront (only for non-video interviews or when GCP fails)
    if (isAiInterview) {
      return `${process.env.NEXT_PUBLIC_CAMPED_ACEPREP_CLOUDFRONT_URL}/${s3RecordingId}`;
    } else {
      return `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/video-meeting/${s3RecordingId}`;
    }
  } catch (error) {
    console.error('Error getting video URL:', error);

    // Final fallback: CloudFront
    if (isAiInterview) {
      return `${process.env.NEXT_PUBLIC_CAMPED_ACEPREP_CLOUDFRONT_URL}/${s3RecordingId}`;
    } else {
      return `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/video-meeting/${s3RecordingId}`;
    }
  }
}

/**
 * Gets HLS playlist URL for tailored-practice interviews
 * These are stored as .m3u8 files in the interview-ai folder in GCP bucket
 * @param practiceId - The practice ID (used as filename without extension)
 * @param organizationId - Organization ID for tenant-specific bucket
 * @returns Promise<string> - The HLS playlist URL to use
 */
export async function getHLSPlaylistUrl(
  practiceId?: string,
  organizationId?: string,
): Promise<string> {
  if (!practiceId) {
    return '';
  }

  // Return the direct API endpoint URL
  const directUrl = `/api/hls-playlist?practiceId=${encodeURIComponent(practiceId)}&organizationId=${
    organizationId || ''
  }`;

  return directUrl;
}
