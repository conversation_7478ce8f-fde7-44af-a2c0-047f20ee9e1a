import { VIDEO_INTERVIEW_COMPREHENSIVE_FEEDBACK_SYSTEM_PROMPT } from '../../constants/video-interview';

export const VideoInterviewComprehensiveFeedbackStrategy = async (input: any): Promise<string> => {
  const { role = 'General', level = 'Mid-level', interviewType = 'General Interview' } = input;

  return VIDEO_INTERVIEW_COMPREHENSIVE_FEEDBACK_SYSTEM_PROMPT.replace(/{role}/g, role)
    .replace(/{level}/g, level)
    .replace(/{interviewType}/g, interviewType);
};
