import { AI_INTERVIEW_COMPREHENSIVE_FEEDBACK_SYSTEM_PROMPT } from '../../constants/ai-interview/comprehensive-feedback-system-prompt';

export const AIInterviewComprehensiveFeedbackStrategy = async (input: any): Promise<string> => {
  const { 
    role = 'General', 
    level = 'Mid-level', 
    candidateName = 'Candidate',
    jobDescription = 'Not provided',
    requiredSkills = 'Not specified'
  } = input;

  return AI_INTERVIEW_COMPREHENSIVE_FEEDBACK_SYSTEM_PROMPT
    .replace(/{role}/g, role)
    .replace(/{level}/g, level)
    .replace(/{candidateName}/g, candidateName)
    .replace(/{jobDescription}/g, jobDescription)
    .replace(/{requiredSkills}/g, requiredSkills);
};
