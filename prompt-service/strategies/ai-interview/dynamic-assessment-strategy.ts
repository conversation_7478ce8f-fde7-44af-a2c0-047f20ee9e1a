import type { AssessmentCriteria, AssessmentTemplate } from '@/types/assessment';

export interface DynamicAssessmentInput {
  role: string;
  level: string;
  candidateName: string;
  jobDescription: string;
  requiredSkills: string;
  coreValues: string;
  assessmentCriteria?: AssessmentCriteria[];
  assessmentTemplate?: AssessmentTemplate;
}

export const generateDynamicAssessmentPrompt = (input: DynamicAssessmentInput): string => {
  const {
    role,
    level,
    candidateName,
    jobDescription,
    requiredSkills,
    coreValues,
    assessmentCriteria,
    assessmentTemplate,
  } = input;

  // Use custom criteria or template criteria
  const criteria = assessmentCriteria || assessmentTemplate?.criteria || [];

  // Generate criteria-specific instructions
  const criteriaInstructions = criteria.map((criterion, index) => {
    const scaleDescription = generateScaleDescription(criterion);
    return `${index + 1}. **${criterion.name}** (Weight: ${criterion.weight}%)
   - Description: ${criterion.description}
   - Scale: ${scaleDescription}
   - Category: ${criterion.category}
   - Required: ${criterion.isRequired ? 'Yes' : 'No'}`;
  }).join('\n\n');

  // Generate JSON response format based on criteria
  const responseFormat = generateResponseFormat(criteria);

  return `You are a senior AI interviewer and hiring expert conducting a comprehensive assessment of this AI-powered interview recording. Your primary objective is to rigorously evaluate whether the candidate is a good fit for the ${role} position at ${level} level.

Context:
- Role: ${role}
- Level: ${level}
- Candidate: ${candidateName}
- Job Description: ${jobDescription}
- Required Skills: ${requiredSkills}
- Organization Core Values: ${coreValues}

This is an AI-conducted interview where the candidate answered questions posed by an AI interviewer. Analyze the candidate's responses, communication style, technical knowledge, and overall presentation.

**CRITICAL REQUIREMENTS:**
1. **COMPLETE TRANSCRIPT**: You MUST extract and document every single word spoken during the interview - both questions asked by the AI interviewer and responses given by the candidate. This is mandatory for audit and review purposes.
2. **INTERVIEW SUMMARY**: Provide a concise overview of all topics discussed, key points covered, and main themes that emerged during the interview.

**CUSTOM ASSESSMENT CRITERIA:**
${criteria.length > 0 ? `
You must evaluate the candidate based on the following specific criteria defined for this interview:

${criteriaInstructions}

**ASSESSMENT INSTRUCTIONS:**
- Evaluate each criterion carefully based on the candidate's performance during the interview
- Provide specific evidence from the interview to support your scores
- Use the exact scale defined for each criterion
- Consider the weight of each criterion when making overall recommendations
- All required criteria must be evaluated
` : `
**STANDARD ASSESSMENT FRAMEWORK:**
Since no custom criteria were defined, use the standard assessment framework:

**TECHNICAL COMPETENCY (Weight: 35%)**
- Role-specific technical knowledge and skills
- Problem-solving approach and methodology
- Understanding of industry best practices
- Ability to explain complex concepts clearly
- Technical depth appropriate for the level

**COMMUNICATION SKILLS (Weight: 25%)**
- Clarity and articulation of thoughts
- Professional communication style
- Ability to structure responses logically
- Active listening and comprehension
- Confidence in delivery

**BEHAVIORAL ASSESSMENT (Weight: 25%)**
- Cultural fit with organization values
- Teamwork and collaboration indicators
- Leadership potential (if applicable)
- Problem-solving approach
- Adaptability and learning mindset

**OVERALL EVALUATION (Weight: 15%)**
- Overall impression and recommendation
- Readiness for the role
- Areas for development
- Hiring recommendation
`}

**PROCTORING ASSESSMENT:**
Evaluate the following proctoring aspects:
- Camera angle and video quality
- Professional attire and presentation
- Eye contact and body language
- Background and environment
- Suspicious activity or distractions
- AI assistance indicators (conservative scoring)
- Response authenticity
- Words per minute calculation
- Overall cheating risk level (LOW/MEDIUM/HIGH/CRITICAL)

**RESPONSE FORMAT (Valid JSON only):**
${responseFormat}

**CRITICAL INSTRUCTIONS:**
1. **MANDATORY TRANSCRIPT**: You MUST provide a complete verbatim transcript of the entire interview. This is non-negotiable and required for legal and audit purposes.
2. **Evidence-Based Assessment**: Every score must be supported by specific examples from the interview
3. **Criterion-Specific Evaluation**: Follow the exact scales and weights defined for each criterion
4. **Comprehensive Analysis**: Consider both strengths and areas for improvement
5. **Professional Judgment**: Provide actionable feedback and clear recommendations

Analyze the interview recording and provide your assessment in the specified JSON format.`;
};

const generateScaleDescription = (criterion: AssessmentCriteria): string => {
  const { scale } = criterion;

  switch (scale.type) {
    case 'numeric':
      return `Rate from ${scale.min || 1} to ${scale.max || 10}${scale.description ? ` - ${scale.description}` : ''}`;
    case 'categorical':
      const labels = scale.labels?.join(', ') || '';
      const description = scale.description || '';
      return `Select from: ${labels}${description ? ` - ${description}` : ''}`;
    case 'boolean':
      return `Yes/No assessment${scale.description ? ` - ${scale.description}` : ''}`;
    default:
      return 'Custom scale';
  }
};

const generateResponseFormat = (criteria: AssessmentCriteria[]): string => {
  if (criteria.length === 0) {
    // Return standard format if no custom criteria
    return `{
  "overall_score": "Overall score (0-100) as integer",
  "impact": "Impact assessment score (0-100) as integer",
  "clarity": "Communication clarity score (0-100) as integer",
  "passion": "Passion and enthusiasm score (0-100) as integer",
  "confidence": "Confidence level score (0-100) as integer",
  "communication": "Overall communication score (0-100) as integer",
  "language_proficiency": "Language proficiency score (0-100) as integer",
  "proctoring": {
    "camera_angle": "Assessment of camera angle and video quality",
    "professional_attire": "Assessment of professional presentation",
    "eye_contact": "Evaluation of eye contact and body language",
    "suspicious_activity": "Any suspicious behavior or distractions",
    "background_analysis": "Assessment of background and environment",
    "response_authenticity": "Evaluation of response genuineness",
    "words_per_minute": "Average speaking pace calculation",
    "ai_assistance_score": "Conservative AI assistance likelihood score (0-100)",
    "cheating_risk_level": "Risk level: LOW/MEDIUM/HIGH/CRITICAL"
  },
  "areas_of_improvement": "Areas for improvement score (0-100) as integer",
  "job_fit": {
    "gaps": {
      "score": "Skill gaps score (0-100) as integer",
      "feedback": "Detailed feedback on gaps"
    },
    "alignment": {
      "score": "Role alignment score (0-100) as integer", 
      "feedback": "Assessment of role fit"
    },
    "suitability": {
      "score": "Overall suitability score (0-100) as integer",
      "feedback": "Overall assessment"
    }
  },
  "complete_transcript": {
    "questions_and_answers": "Complete verbatim transcript",
    "total_duration": "Estimated interview duration",
    "question_count": "Total questions asked"
  },
  "interview_summary": "Comprehensive summary of interview",
  "recommendation": "HIRE/NOT_HIRE/MAYBE with detailed reasoning"
}`;
  }

  // Generate dynamic format based on custom criteria
  const criteriaFormat = criteria.map(criterion => {
    const scoreType = criterion.scale.type === 'numeric' ? 'number' : 'string';
    return `    "${criterion.id}": {
      "score": "${scoreType === 'number' ? 'Numeric score based on defined scale' : 'Value from defined scale options'}",
      "feedback": "Detailed feedback for ${criterion.name}",
      "evidence": "Specific examples from interview supporting this assessment",
      "weight": ${criterion.weight}
    }`;
  }).join(',\n');

  return `{
  "assessment_results": {
${criteriaFormat}
  },
  "overall_assessment": {
    "weighted_score": "Calculated weighted average of all criteria scores (0-100)",
    "recommendation": "HIRE/NOT_HIRE/MAYBE",
    "summary": "Comprehensive summary based on all assessment criteria",
    "strengths": "Key strengths identified across all criteria",
    "areas_for_improvement": "Areas needing development across all criteria"
  },
  "proctoring": {
    "camera_angle": "Assessment of camera angle and video quality",
    "professional_attire": "Assessment of professional presentation",
    "eye_contact": "Evaluation of eye contact and body language",
    "suspicious_activity": "Any suspicious behavior or distractions",
    "background_analysis": "Assessment of background and environment",
    "response_authenticity": "Evaluation of response genuineness",
    "words_per_minute": "Average speaking pace calculation",
    "ai_assistance_score": "Conservative AI assistance likelihood score (0-100)",
    "cheating_risk_level": "Risk level: LOW/MEDIUM/HIGH/CRITICAL"
  },
  "complete_transcript": {
    "questions_and_answers": "Complete verbatim transcript of entire interview",
    "total_duration": "Estimated total interview duration",
    "question_count": "Total number of questions asked"
  },
  "interview_summary": "Comprehensive overview of topics discussed and key themes",
  "detailed_analysis": "In-depth analysis of candidate performance across all criteria"
}`;
};
