import { AssessmentTemplate, AssessmentCriteria, COMMON_SCALES } from '@/types/assessment';

// Technical Roles Template
export const TECHNICAL_TEMPLATE: Omit<AssessmentTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'Technical Roles Assessment',
  description: 'Comprehensive assessment for software engineers, developers, and technical roles',
  roleType: 'technical',
  isDefault: true,
  isActive: true,
  criteria: [
    {
      id: 'tech_problem_solving',
      name: 'Problem Solving',
      description: 'Ability to break down complex problems and find effective solutions',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'technical',
      isRequired: true,
    },
    {
      id: 'tech_knowledge',
      name: 'Technical Knowledge',
      description: 'Depth of knowledge in relevant technologies and frameworks',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'technical',
      isRequired: true,
    },
    {
      id: 'code_quality',
      name: 'Code Quality & Best Practices',
      description: 'Understanding of clean code, design patterns, and best practices',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'technical',
      isRequired: true,
    },
    {
      id: 'system_design',
      name: 'System Design Thinking',
      description: 'Ability to design scalable and maintainable systems',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'technical',
      isRequired: false,
    },
    {
      id: 'communication',
      name: 'Technical Communication',
      description: 'Ability to explain technical concepts clearly',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'communication',
      isRequired: true,
    },
  ],
};

// Language Proficiency Template
export const LANGUAGE_TEMPLATE: Omit<AssessmentTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'Language Proficiency Assessment',
  description: 'Comprehensive language assessment based on CEFR standards',
  roleType: 'language',
  isDefault: true,
  isActive: true,
  criteria: [
    {
      id: 'speaking_fluency',
      name: 'Speaking Fluency',
      description: 'Ability to speak smoothly and naturally without hesitation',
      scale: COMMON_SCALES.language_cefr,
      weight: 25,
      category: 'communication',
      isRequired: true,
    },
    {
      id: 'grammar_accuracy',
      name: 'Grammar Accuracy',
      description: 'Correct use of grammar structures and syntax',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'communication',
      isRequired: true,
    },
    {
      id: 'vocabulary_range',
      name: 'Vocabulary Range',
      description: 'Breadth and appropriateness of vocabulary usage',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'communication',
      isRequired: true,
    },
    {
      id: 'pronunciation',
      name: 'Pronunciation',
      description: 'Clarity and accuracy of pronunciation',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'communication',
      isRequired: true,
    },
    {
      id: 'comprehension',
      name: 'Listening Comprehension',
      description: 'Ability to understand spoken language and respond appropriately',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'communication',
      isRequired: true,
    },
  ],
};

// Sales Roles Template
export const SALES_TEMPLATE: Omit<AssessmentTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'Sales Roles Assessment',
  description: 'Assessment for sales representatives, account managers, and business development roles',
  roleType: 'sales',
  isDefault: true,
  isActive: true,
  criteria: [
    {
      id: 'negotiation_skills',
      name: 'Negotiation Skills',
      description: 'Ability to negotiate effectively and reach win-win outcomes',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'domain_specific',
      isRequired: true,
    },
    {
      id: 'relationship_building',
      name: 'Customer Relationship Building',
      description: 'Ability to build rapport and maintain long-term customer relationships',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'product_knowledge',
      name: 'Product Knowledge',
      description: 'Understanding of products/services and ability to communicate value',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'domain_specific',
      isRequired: true,
    },
    {
      id: 'persuasion_ability',
      name: 'Persuasion & Influence',
      description: 'Ability to persuade and influence customer decisions',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'objection_handling',
      name: 'Objection Handling',
      description: 'Skill in addressing customer concerns and objections',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'domain_specific',
      isRequired: true,
    },
  ],
};

// Leadership Template
export const LEADERSHIP_TEMPLATE: Omit<AssessmentTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'Leadership Assessment',
  description: 'Assessment for managers, directors, and leadership positions',
  roleType: 'leadership',
  isDefault: true,
  isActive: true,
  criteria: [
    {
      id: 'team_management',
      name: 'Team Management',
      description: 'Ability to lead, motivate, and manage team members effectively',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'strategic_thinking',
      name: 'Strategic Thinking',
      description: 'Ability to think long-term and develop strategic plans',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'decision_making',
      name: 'Decision Making',
      description: 'Quality of decision-making process and outcomes',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'conflict_resolution',
      name: 'Conflict Resolution',
      description: 'Ability to resolve conflicts and manage difficult situations',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'vision_communication',
      name: 'Vision & Communication',
      description: 'Ability to articulate vision and communicate effectively with stakeholders',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'communication',
      isRequired: true,
    },
  ],
};

// Non-Technical Template
export const NON_TECHNICAL_TEMPLATE: Omit<AssessmentTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
  name: 'Non-Technical Roles Assessment',
  description: 'General assessment for business roles and non-technical positions',
  roleType: 'non_technical',
  isDefault: true,
  isActive: true,
  criteria: [
    {
      id: 'communication_skills',
      name: 'Communication Skills',
      description: 'Verbal and written communication effectiveness',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'communication',
      isRequired: true,
    },
    {
      id: 'analytical_thinking',
      name: 'Analytical Thinking',
      description: 'Ability to analyze information and solve problems',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 25,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'teamwork',
      name: 'Teamwork & Collaboration',
      description: 'Ability to work effectively in team environments',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 20,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'adaptability',
      name: 'Adaptability',
      description: 'Flexibility and ability to adapt to changing situations',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'behavioral',
      isRequired: true,
    },
    {
      id: 'initiative',
      name: 'Initiative & Proactivity',
      description: 'Self-motivation and ability to take initiative',
      scale: COMMON_SCALES.numeric_1_10,
      weight: 15,
      category: 'behavioral',
      isRequired: true,
    },
  ],
};

export const DEFAULT_TEMPLATES = [
  TECHNICAL_TEMPLATE,
  LANGUAGE_TEMPLATE,
  SALES_TEMPLATE,
  LEADERSHIP_TEMPLATE,
  NON_TECHNICAL_TEMPLATE,
];
